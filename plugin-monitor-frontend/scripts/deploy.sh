#!/bin/bash

# Plugin Monitor Frontend 部署脚本
# 使用方法: ./scripts/deploy.sh [environment]
# 环境: development, staging, production

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
ENVIRONMENT=${1:-development}
if [[ ! "$ENVIRONMENT" =~ ^(development|staging|production)$ ]]; then
    log_error "无效的环境参数: $ENVIRONMENT"
    log_info "支持的环境: development, staging, production"
    exit 1
fi

log_info "开始部署到 $ENVIRONMENT 环境..."

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

# 检查必要的工具
check_dependencies() {
    log_info "检查依赖工具..."
    
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装"
        exit 1
    fi
    
    if ! command -v git &> /dev/null; then
        log_error "git 未安装"
        exit 1
    fi
    
    log_success "依赖工具检查完成"
}

# 检查环境变量
check_environment() {
    log_info "检查环境变量..."
    
    case $ENVIRONMENT in
        development)
            ENV_FILE=".env.local"
            ;;
        staging)
            ENV_FILE=".env.staging"
            ;;
        production)
            ENV_FILE=".env.production"
            ;;
    esac
    
    if [[ ! -f "$ENV_FILE" ]]; then
        log_warning "环境变量文件 $ENV_FILE 不存在，使用默认配置"
    else
        log_success "环境变量文件 $ENV_FILE 已找到"
    fi
}

# 安装依赖
install_dependencies() {
    log_info "安装依赖包..."
    
    if [[ -f "package-lock.json" ]]; then
        npm ci
    else
        npm install
    fi
    
    log_success "依赖包安装完成"
}

# 运行测试
run_tests() {
    if [[ "$ENVIRONMENT" != "development" ]]; then
        log_info "运行测试..."
        
        # 类型检查
        if npm run type-check &> /dev/null; then
            log_success "TypeScript 类型检查通过"
        else
            log_warning "TypeScript 类型检查失败，继续部署..."
        fi
        
        # ESLint 检查
        if npm run lint &> /dev/null; then
            log_success "ESLint 检查通过"
        else
            log_warning "ESLint 检查失败，继续部署..."
        fi
        
        # 单元测试（如果存在）
        if npm run test:ci &> /dev/null; then
            log_success "单元测试通过"
        else
            log_warning "单元测试失败或不存在，继续部署..."
        fi
    fi
}

# 构建项目
build_project() {
    log_info "构建项目..."
    
    # 设置环境变量
    export NODE_ENV=$([[ "$ENVIRONMENT" == "development" ]] && echo "development" || echo "production")
    
    # 构建
    if [[ "$ENVIRONMENT" == "development" ]]; then
        npm run dev &
        DEV_PID=$!
        log_success "开发服务器已启动 (PID: $DEV_PID)"
        log_info "访问地址: http://localhost:3000"
        
        # 等待用户停止
        read -p "按 Enter 键停止开发服务器..."
        kill $DEV_PID
        log_success "开发服务器已停止"
    else
        npm run build
        log_success "项目构建完成"
    fi
}

# 部署到服务器
deploy_to_server() {
    if [[ "$ENVIRONMENT" == "development" ]]; then
        return 0
    fi
    
    log_info "部署到 $ENVIRONMENT 服务器..."
    
    case $ENVIRONMENT in
        staging)
            # 部署到测试服务器
            DEPLOY_HOST=${STAGING_HOST:-"staging.example.com"}
            DEPLOY_PATH=${STAGING_PATH:-"/var/www/plugin-monitor-staging"}
            ;;
        production)
            # 部署到生产服务器
            DEPLOY_HOST=${PRODUCTION_HOST:-"production.example.com"}
            DEPLOY_PATH=${PRODUCTION_PATH:-"/var/www/plugin-monitor"}
            ;;
    esac
    
    if [[ -n "$DEPLOY_HOST" ]]; then
        log_info "上传文件到 $DEPLOY_HOST:$DEPLOY_PATH"
        
        # 创建部署包
        tar -czf deploy.tar.gz .next package.json package-lock.json public
        
        # 上传到服务器（需要配置SSH密钥）
        scp deploy.tar.gz "$DEPLOY_HOST:$DEPLOY_PATH/"
        
        # 在服务器上解压和重启
        ssh "$DEPLOY_HOST" "cd $DEPLOY_PATH && tar -xzf deploy.tar.gz && npm install --production && pm2 restart plugin-monitor"
        
        # 清理临时文件
        rm deploy.tar.gz
        
        log_success "部署到 $ENVIRONMENT 服务器完成"
    else
        log_warning "未配置服务器信息，跳过部署"
    fi
}

# Docker 部署
deploy_with_docker() {
    if [[ "$ENVIRONMENT" == "development" ]]; then
        return 0
    fi
    
    log_info "使用 Docker 部署..."
    
    # 构建 Docker 镜像
    DOCKER_TAG="plugin-monitor-frontend:$ENVIRONMENT-$(date +%Y%m%d-%H%M%S)"
    
    docker build -t "$DOCKER_TAG" .
    
    # 标记为最新版本
    docker tag "$DOCKER_TAG" "plugin-monitor-frontend:$ENVIRONMENT-latest"
    
    log_success "Docker 镜像构建完成: $DOCKER_TAG"
    
    # 推送到镜像仓库（如果配置了）
    if [[ -n "$DOCKER_REGISTRY" ]]; then
        docker tag "$DOCKER_TAG" "$DOCKER_REGISTRY/$DOCKER_TAG"
        docker push "$DOCKER_REGISTRY/$DOCKER_TAG"
        log_success "Docker 镜像已推送到仓库"
    fi
}

# 健康检查
health_check() {
    if [[ "$ENVIRONMENT" == "development" ]]; then
        return 0
    fi
    
    log_info "执行健康检查..."
    
    # 检查服务是否正常运行
    HEALTH_URL=${HEALTH_CHECK_URL:-"http://localhost:3000/api/health"}
    
    for i in {1..5}; do
        if curl -f "$HEALTH_URL" &> /dev/null; then
            log_success "健康检查通过"
            return 0
        fi
        log_warning "健康检查失败，重试 $i/5..."
        sleep 10
    done
    
    log_error "健康检查失败"
    return 1
}

# 回滚函数
rollback() {
    log_error "部署失败，执行回滚..."
    
    # 这里可以添加回滚逻辑
    # 例如：恢复上一个版本的代码、重启服务等
    
    log_info "回滚完成"
}

# 主函数
main() {
    log_info "=== Plugin Monitor Frontend 部署脚本 ==="
    log_info "环境: $ENVIRONMENT"
    log_info "时间: $(date)"
    log_info "========================================"
    
    # 设置错误处理
    trap rollback ERR
    
    # 执行部署步骤
    check_dependencies
    check_environment
    install_dependencies
    run_tests
    build_project
    
    # 根据环境选择部署方式
    if [[ "$USE_DOCKER" == "true" ]]; then
        deploy_with_docker
    else
        deploy_to_server
    fi
    
    health_check
    
    log_success "=== 部署完成 ==="
    log_info "环境: $ENVIRONMENT"
    log_info "时间: $(date)"
    log_info "================="
}

# 执行主函数
main "$@"
