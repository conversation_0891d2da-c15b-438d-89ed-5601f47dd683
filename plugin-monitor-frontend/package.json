{"name": "plugin-monitor-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"next": "14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.3", "antd": "^5.12.8", "@ant-design/icons": "^5.2.6", "@ant-design/nextjs-registry": "^1.0.0", "tailwindcss": "^3.3.6", "@tailwindcss/typography": "^0.5.10", "zustand": "^4.4.7", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "echarts": "^5.4.3", "echarts-for-react": "^3.0.2", "socket.io-client": "^4.7.4", "dayjs": "^1.11.10", "lodash": "^4.17.21", "clsx": "^2.0.0", "swr": "^2.2.4"}, "devDependencies": {"@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/lodash": "^4.14.202", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "eslint": "^8.56.0", "eslint-config-next": "14.0.4", "prettier": "^3.1.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.6", "@testing-library/user-event": "^14.5.1"}}