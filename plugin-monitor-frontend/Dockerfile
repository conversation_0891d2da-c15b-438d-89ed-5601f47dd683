# Plugin Monitor Frontend Dockerfile
# 多阶段构建，优化镜像大小

# 阶段1: 依赖安装
FROM node:18-alpine AS deps
LABEL stage=deps

# 安装必要的系统依赖
RUN apk add --no-cache libc6-compat

# 设置工作目录
WORKDIR /app

# 复制包管理文件
COPY package.json package-lock.json* ./

# 安装依赖
RUN npm ci --only=production && npm cache clean --force

# 阶段2: 构建应用
FROM node:18-alpine AS builder
LABEL stage=builder

WORKDIR /app

# 复制依赖
COPY --from=deps /app/node_modules ./node_modules

# 复制源代码
COPY . .

# 设置环境变量
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

# 构建应用
RUN npm run build

# 阶段3: 运行时镜像
FROM node:18-alpine AS runner
LABEL stage=runner

WORKDIR /app

# 创建非root用户
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# 设置环境变量
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# 复制必要文件
COPY --from=builder /app/public ./public
COPY --from=builder /app/package.json ./package.json

# 复制构建产物
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# 创建健康检查脚本
RUN echo '#!/bin/sh\ncurl -f http://localhost:3000/api/health || exit 1' > /app/healthcheck.sh && \
    chmod +x /app/healthcheck.sh

# 切换到非root用户
USER nextjs

# 暴露端口
EXPOSE 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD /app/healthcheck.sh

# 启动应用
CMD ["node", "server.js"]

# 元数据标签
LABEL maintainer="Plugin Monitor Team"
LABEL version="1.0.0"
LABEL description="Plugin Monitor Frontend Application"
LABEL org.opencontainers.image.source="https://github.com/your-org/plugin-monitor"
LABEL org.opencontainers.image.documentation="https://github.com/your-org/plugin-monitor/blob/main/README.md"
