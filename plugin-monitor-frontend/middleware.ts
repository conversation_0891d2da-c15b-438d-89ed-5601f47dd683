import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// 需要认证的路径
const protectedPaths = [
  '/dashboard',
  '/api/plugin',
  '/api/config',
];

// 公开访问的路径
const publicPaths = [
  '/login',
  '/api/auth',
  '/api/health',
];

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // 检查是否是受保护的路径
  const isProtectedPath = protectedPaths.some(path => pathname.startsWith(path));
  const isPublicPath = publicPaths.some(path => pathname.startsWith(path));

  // 如果是公开路径，直接通过
  if (isPublicPath) {
    return NextResponse.next();
  }

  // 如果是受保护的路径，检查认证
  if (isProtectedPath) {
    // 在实际项目中，这里应该验证 JWT token 或 session
    const authToken = request.headers.get('authorization') || request.cookies.get('auth-token')?.value;
    
    // 模拟认证检查
    if (!authToken && pathname.startsWith('/api/')) {
      return NextResponse.json(
        {
          success: false,
          error: '未授权访问',
          timestamp: Date.now(),
        },
        { status: 401 }
      );
    }

    // 如果是页面访问且未认证，重定向到登录页
    if (!authToken && !pathname.startsWith('/api/')) {
      const loginUrl = new URL('/login', request.url);
      loginUrl.searchParams.set('redirect', pathname);
      return NextResponse.redirect(loginUrl);
    }
  }

  // 添加安全头
  const response = NextResponse.next();
  
  // CORS 头
  response.headers.set('Access-Control-Allow-Origin', '*');
  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  // 安全头
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');

  // API 限流（简单实现）
  if (pathname.startsWith('/api/')) {
    const clientIP = request.ip || request.headers.get('x-forwarded-for') || 'unknown';
    
    // 在实际项目中，这里应该使用 Redis 或其他存储来实现限流
    // 这里只是示例
    response.headers.set('X-RateLimit-Limit', '100');
    response.headers.set('X-RateLimit-Remaining', '99');
    response.headers.set('X-RateLimit-Reset', String(Date.now() + 60000));
  }

  return response;
}

// 配置中间件匹配的路径
export const config = {
  matcher: [
    /*
     * 匹配所有路径除了:
     * - _next/static (静态文件)
     * - _next/image (图片优化)
     * - favicon.ico (网站图标)
     * - public 文件夹中的文件
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};
