// 插件类型枚举
export enum PluginType {
  INBOUND = 'inbound',
  OUTBOUND = 'outbound',
  RULE = 'rule',
  FLOW = 'flow',
}

// 插件状态枚举
export enum PluginStatus {
  RUNNING = 'running',
  IDLE = 'idle',
  ERROR = 'error',
  STOPPED = 'stopped',
}

// 插件协议类型
export enum PluginProtocol {
  MODBUS = 'modbus',
  HTTP_CLIENT = 'http_client',
  MQTT = 'mqtt',
  WEBSOCKET = 'websocket',
  FANUC = 'fanuc',
  SIEMENS = 'siemens',
}

// 插件基础信息
export interface PluginInfo {
  id: string;
  name: string;
  type: PluginType;
  protocol: PluginProtocol;
  description?: string;
  version?: string;
  author?: string;
  tags?: string[];
}

// 插件状态详情
export interface PluginStatusDetails {
  memory_usage?: string;
  cpu_usage?: string;
  last_activity?: string;
  error_count?: number;
  success_count?: number;
  uptime?: number;
  [key: string]: any;
}

// 插件状态
export interface PluginState {
  plugin_id: string;
  plugin_type: string;
  status: PluginStatus;
  timestamp: number;
  details?: PluginStatusDetails;
}

// 插件数据
export interface PluginData {
  plugin_id: string;
  plugin_type: string;
  data_type: string;
  data: any;
  timestamp: number;
  metadata?: Record<string, any>;
}

// 插件配置
export interface PluginConfig {
  id: string;
  type: PluginType;
  protocol: PluginProtocol;
  name: string;
  enabled: boolean;
  config: Record<string, any>;
  created_at?: string;
  updated_at?: string;
}

// 插件性能指标
export interface PluginMetrics {
  plugin_id: string;
  timestamp: number;
  cpu_usage: number;
  memory_usage: number;
  network_io: {
    bytes_sent: number;
    bytes_received: number;
  };
  error_rate: number;
  throughput: number;
}

// 插件日志
export interface PluginLog {
  id: string;
  plugin_id: string;
  level: 'debug' | 'info' | 'warning' | 'error';
  message: string;
  timestamp: number;
  context?: Record<string, any>;
}

// API 响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: number;
}

// 分页响应
export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

// WebSocket 消息类型
export interface WebSocketMessage {
  type: 'plugin_status' | 'plugin_data' | 'system_event';
  payload: any;
  timestamp: number;
}
