// 仪表板组件类型
export enum WidgetType {
  CHART = 'chart',
  METRIC = 'metric',
  TABLE = 'table',
  STATUS = 'status',
  GAUGE = 'gauge',
  TEXT = 'text',
  IMAGE = 'image',
  BUTTON = 'button',
}

// 图表类型
export enum ChartType {
  LINE = 'line',
  BAR = 'bar',
  PIE = 'pie',
  GAUGE = 'gauge',
  SCATTER = 'scatter',
  HEATMAP = 'heatmap',
}

// 组件位置和尺寸
export interface WidgetLayout {
  x: number;
  y: number;
  width: number;
  height: number;
  minWidth?: number;
  minHeight?: number;
  maxWidth?: number;
  maxHeight?: number;
}

// 数据绑定配置
export interface DataBinding {
  source: 'plugin' | 'api' | 'static';
  plugin_id?: string;
  data_path?: string;
  api_endpoint?: string;
  static_value?: any;
  refresh_interval?: number;
}

// 样式配置
export interface WidgetStyle {
  backgroundColor?: string;
  borderColor?: string;
  borderWidth?: number;
  borderRadius?: number;
  fontSize?: number;
  fontColor?: string;
  fontWeight?: 'normal' | 'bold';
  textAlign?: 'left' | 'center' | 'right';
  padding?: number;
  margin?: number;
}

// 组件配置基类
export interface BaseWidgetConfig {
  id: string;
  type: WidgetType;
  title?: string;
  layout: WidgetLayout;
  style?: WidgetStyle;
  dataBinding?: DataBinding;
  visible?: boolean;
  interactive?: boolean;
}

// 图表组件配置
export interface ChartWidgetConfig extends BaseWidgetConfig {
  type: WidgetType.CHART;
  chartType: ChartType;
  chartOptions: {
    xAxis?: any;
    yAxis?: any;
    series?: any[];
    legend?: any;
    tooltip?: any;
    [key: string]: any;
  };
}

// 指标组件配置
export interface MetricWidgetConfig extends BaseWidgetConfig {
  type: WidgetType.METRIC;
  format?: 'number' | 'percentage' | 'currency' | 'bytes';
  precision?: number;
  unit?: string;
  threshold?: {
    warning?: number;
    critical?: number;
  };
}

// 状态组件配置
export interface StatusWidgetConfig extends BaseWidgetConfig {
  type: WidgetType.STATUS;
  statusMapping?: Record<string, {
    label: string;
    color: string;
    icon?: string;
  }>;
}

// 仪表盘组件配置
export interface GaugeWidgetConfig extends BaseWidgetConfig {
  type: WidgetType.GAUGE;
  min: number;
  max: number;
  unit?: string;
  thresholds?: Array<{
    value: number;
    color: string;
  }>;
}

// 文本组件配置
export interface TextWidgetConfig extends BaseWidgetConfig {
  type: WidgetType.TEXT;
  content: string;
  markdown?: boolean;
}

// 按钮组件配置
export interface ButtonWidgetConfig extends BaseWidgetConfig {
  type: WidgetType.BUTTON;
  label: string;
  action: {
    type: 'api' | 'navigation' | 'custom';
    endpoint?: string;
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
    payload?: any;
    url?: string;
    script?: string;
  };
}

// 联合类型
export type WidgetConfig = 
  | ChartWidgetConfig
  | MetricWidgetConfig
  | StatusWidgetConfig
  | GaugeWidgetConfig
  | TextWidgetConfig
  | ButtonWidgetConfig;

// 仪表板配置
export interface DashboardConfig {
  id: string;
  name: string;
  description?: string;
  layout: {
    columns: number;
    rowHeight: number;
    margin: [number, number];
    containerPadding: [number, number];
  };
  widgets: WidgetConfig[];
  theme?: {
    primaryColor?: string;
    backgroundColor?: string;
    textColor?: string;
  };
  created_at?: string;
  updated_at?: string;
  created_by?: string;
}

// 仪表板模板
export interface DashboardTemplate {
  id: string;
  name: string;
  description?: string;
  category: string;
  preview_image?: string;
  config: Omit<DashboardConfig, 'id' | 'created_at' | 'updated_at' | 'created_by'>;
  tags?: string[];
}

// 用户配置
export interface UserConfig {
  theme: 'light' | 'dark' | 'auto';
  language: 'zh-CN' | 'en-US';
  timezone: string;
  dateFormat: string;
  numberFormat: string;
  defaultDashboard?: string;
  sidebarCollapsed?: boolean;
  refreshInterval?: number;
  notifications: {
    enabled: boolean;
    sound: boolean;
    desktop: boolean;
    email: boolean;
  };
}
