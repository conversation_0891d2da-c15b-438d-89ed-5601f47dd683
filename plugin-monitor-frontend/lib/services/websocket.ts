import { io, Socket } from 'socket.io-client';
import { WebSocketMessage, PluginState, PluginData } from '@/lib/types/plugin';

export type WebSocketEventHandler = (data: any) => void;

export interface WebSocketSubscription {
  event: string;
  handler: WebSocketEventHandler;
}

class WebSocketService {
  private socket: Socket | null = null;
  private subscriptions: Map<string, Set<WebSocketEventHandler>> = new Map();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectInterval = 3000;
  private isConnecting = false;

  constructor() {
    this.connect();
  }

  private connect() {
    if (this.isConnecting || this.socket?.connected) {
      return;
    }

    this.isConnecting = true;
    const wsUrl = process.env.NEXT_PUBLIC_WEBSOCKET_URL || 'ws://localhost:9999';

    try {
      this.socket = io(wsUrl, {
        transports: ['websocket'],
        timeout: 5000,
        reconnection: true,
        reconnectionAttempts: this.maxReconnectAttempts,
        reconnectionDelay: this.reconnectInterval,
      });

      this.setupEventHandlers();
    } catch (error) {
      console.error('WebSocket connection failed:', error);
      this.isConnecting = false;
      this.scheduleReconnect();
    }
  }

  private setupEventHandlers() {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('WebSocket connected');
      this.isConnecting = false;
      this.reconnectAttempts = 0;
      this.emit('connection', { status: 'connected' });
    });

    this.socket.on('disconnect', (reason) => {
      console.log('WebSocket disconnected:', reason);
      this.emit('connection', { status: 'disconnected', reason });
      
      if (reason === 'io server disconnect') {
        // 服务器主动断开连接，需要手动重连
        this.scheduleReconnect();
      }
    });

    this.socket.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error);
      this.isConnecting = false;
      this.emit('connection', { status: 'error', error: error.message });
      this.scheduleReconnect();
    });

    // 插件状态更新
    this.socket.on('plugin_status', (data: PluginState) => {
      this.emit('plugin_status', data);
    });

    // 插件数据更新
    this.socket.on('plugin_data', (data: PluginData) => {
      this.emit('plugin_data', data);
    });

    // 系统事件
    this.socket.on('system_event', (data: any) => {
      this.emit('system_event', data);
    });

    // 通用消息处理
    this.socket.on('message', (message: WebSocketMessage) => {
      this.emit(message.type, message.payload);
    });
  }

  private scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached');
      this.emit('connection', { 
        status: 'failed', 
        message: '连接失败，已达到最大重试次数' 
      });
      return;
    }

    this.reconnectAttempts++;
    console.log(`Scheduling reconnect attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);

    setTimeout(() => {
      if (!this.socket?.connected) {
        this.connect();
      }
    }, this.reconnectInterval * this.reconnectAttempts);
  }

  // 订阅事件
  subscribe(event: string, handler: WebSocketEventHandler): () => void {
    if (!this.subscriptions.has(event)) {
      this.subscriptions.set(event, new Set());
    }
    
    this.subscriptions.get(event)!.add(handler);

    // 返回取消订阅函数
    return () => {
      this.unsubscribe(event, handler);
    };
  }

  // 取消订阅
  unsubscribe(event: string, handler: WebSocketEventHandler) {
    const handlers = this.subscriptions.get(event);
    if (handlers) {
      handlers.delete(handler);
      if (handlers.size === 0) {
        this.subscriptions.delete(event);
      }
    }
  }

  // 发送消息
  send(event: string, data: any) {
    if (this.socket?.connected) {
      this.socket.emit(event, data);
    } else {
      console.warn('WebSocket not connected, message not sent:', event, data);
    }
  }

  // 订阅插件状态更新
  subscribeToPluginStatus(pluginIds?: string[]) {
    this.send('subscribe_plugin_status', { plugin_ids: pluginIds });
  }

  // 订阅插件数据更新
  subscribeToPluginData(pluginIds?: string[]) {
    this.send('subscribe_plugin_data', { plugin_ids: pluginIds });
  }

  // 取消订阅插件状态
  unsubscribeFromPluginStatus(pluginIds?: string[]) {
    this.send('unsubscribe_plugin_status', { plugin_ids: pluginIds });
  }

  // 取消订阅插件数据
  unsubscribeFromPluginData(pluginIds?: string[]) {
    this.send('unsubscribe_plugin_data', { plugin_ids: pluginIds });
  }

  // 触发事件
  private emit(event: string, data: any) {
    const handlers = this.subscriptions.get(event);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error('Error in WebSocket event handler:', error);
        }
      });
    }
  }

  // 获取连接状态
  get connected(): boolean {
    return this.socket?.connected || false;
  }

  // 获取连接ID
  get id(): string | undefined {
    return this.socket?.id;
  }

  // 断开连接
  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.subscriptions.clear();
    this.reconnectAttempts = 0;
  }

  // 手动重连
  reconnect() {
    this.disconnect();
    this.reconnectAttempts = 0;
    this.connect();
  }
}

// 创建单例实例
export const websocketService = new WebSocketService();

// React Hook for WebSocket
export const useWebSocket = () => {
  return {
    connected: websocketService.connected,
    subscribe: websocketService.subscribe.bind(websocketService),
    unsubscribe: websocketService.unsubscribe.bind(websocketService),
    send: websocketService.send.bind(websocketService),
    subscribeToPluginStatus: websocketService.subscribeToPluginStatus.bind(websocketService),
    subscribeToPluginData: websocketService.subscribeToPluginData.bind(websocketService),
    unsubscribeFromPluginStatus: websocketService.unsubscribeFromPluginStatus.bind(websocketService),
    unsubscribeFromPluginData: websocketService.unsubscribeFromPluginData.bind(websocketService),
    reconnect: websocketService.reconnect.bind(websocketService),
  };
};
