import { ApiResponse, PaginatedResponse, PluginState, PluginData, PluginConfig } from '@/lib/types/plugin';
import { DashboardConfig, UserConfig } from '@/lib/types/dashboard';

// API 基础配置
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api';

// 通用请求函数
async function request<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<ApiResponse<T>> {
  const url = `${API_BASE_URL}${endpoint}`;
  
  const defaultOptions: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  };

  try {
    const response = await fetch(url, defaultOptions);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('API request failed:', error);
    throw error;
  }
}

// 插件相关 API
export const pluginApi = {
  // 获取所有插件状态
  async getPluginStatus(params?: {
    plugin_type?: string;
    status?: string;
  }): Promise<ApiResponse<PluginState[]>> {
    const searchParams = new URLSearchParams();
    if (params?.plugin_type) searchParams.set('plugin_type', params.plugin_type);
    if (params?.status) searchParams.set('status', params.status);
    
    const query = searchParams.toString();
    return request(`/plugin/status${query ? `?${query}` : ''}`);
  },

  // 获取指定插件状态
  async getPluginStatusById(pluginId: string): Promise<ApiResponse<PluginState>> {
    return request(`/plugin/status/${pluginId}`);
  },

  // 获取插件数据
  async getPluginData(params?: {
    plugin_id?: string;
    data_type?: string;
    limit?: number;
    offset?: number;
  }): Promise<PaginatedResponse<PluginData>> {
    const searchParams = new URLSearchParams();
    if (params?.plugin_id) searchParams.set('plugin_id', params.plugin_id);
    if (params?.data_type) searchParams.set('data_type', params.data_type);
    if (params?.limit) searchParams.set('limit', params.limit.toString());
    if (params?.offset) searchParams.set('offset', params.offset.toString());
    
    const query = searchParams.toString();
    return request(`/plugin/data${query ? `?${query}` : ''}`);
  },

  // 获取指定插件数据
  async getPluginDataById(pluginId: string): Promise<ApiResponse<PluginData[]>> {
    return request(`/plugin/data/${pluginId}`);
  },

  // 执行插件操作
  async executePluginAction(
    pluginId: string,
    action: string,
    payload?: any
  ): Promise<ApiResponse<any>> {
    return request(`/plugin/${pluginId}/action`, {
      method: 'POST',
      body: JSON.stringify({ action, payload }),
    });
  },

  // 获取插件配置
  async getPluginConfigs(): Promise<ApiResponse<PluginConfig[]>> {
    return request('/plugin/config');
  },

  // 更新插件配置
  async updatePluginConfig(
    pluginId: string,
    config: Partial<PluginConfig>
  ): Promise<ApiResponse<PluginConfig>> {
    return request(`/plugin/config/${pluginId}`, {
      method: 'PUT',
      body: JSON.stringify(config),
    });
  },
};

// 仪表板相关 API
export const dashboardApi = {
  // 获取仪表板配置
  async getDashboards(): Promise<ApiResponse<DashboardConfig[]>> {
    return request('/config/dashboard');
  },

  // 获取指定仪表板配置
  async getDashboardById(dashboardId: string): Promise<ApiResponse<DashboardConfig>> {
    return request(`/config/dashboard/${dashboardId}`);
  },

  // 创建仪表板
  async createDashboard(config: Omit<DashboardConfig, 'id' | 'created_at' | 'updated_at'>): Promise<ApiResponse<DashboardConfig>> {
    return request('/config/dashboard', {
      method: 'POST',
      body: JSON.stringify(config),
    });
  },

  // 更新仪表板
  async updateDashboard(
    dashboardId: string,
    config: Partial<DashboardConfig>
  ): Promise<ApiResponse<DashboardConfig>> {
    return request(`/config/dashboard/${dashboardId}`, {
      method: 'PUT',
      body: JSON.stringify(config),
    });
  },

  // 删除仪表板
  async deleteDashboard(dashboardId: string): Promise<ApiResponse<void>> {
    return request(`/config/dashboard/${dashboardId}`, {
      method: 'DELETE',
    });
  },
};

// 用户配置相关 API
export const userApi = {
  // 获取用户配置
  async getUserConfig(): Promise<ApiResponse<UserConfig>> {
    return request('/config/user');
  },

  // 更新用户配置
  async updateUserConfig(config: Partial<UserConfig>): Promise<ApiResponse<UserConfig>> {
    return request('/config/user', {
      method: 'PUT',
      body: JSON.stringify(config),
    });
  },
};

// WebSocket 相关 API
export const websocketApi = {
  // 获取 WebSocket 连接状态
  async getConnectionStatus(): Promise<ApiResponse<{ connected: boolean; uptime: number }>> {
    return request('/ws/status');
  },

  // 订阅数据更新
  async subscribe(subscriptions: string[]): Promise<ApiResponse<void>> {
    return request('/ws/subscribe', {
      method: 'POST',
      body: JSON.stringify({ subscriptions }),
    });
  },

  // 取消订阅
  async unsubscribe(subscriptions: string[]): Promise<ApiResponse<void>> {
    return request('/ws/unsubscribe', {
      method: 'POST',
      body: JSON.stringify({ subscriptions }),
    });
  },

  // WebSocket 健康检查
  async healthCheck(): Promise<ApiResponse<{ status: string; latency: number }>> {
    return request('/ws/health');
  },
};
