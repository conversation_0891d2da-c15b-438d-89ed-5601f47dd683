import useSWR, { SWRConfiguration, mutate } from 'swr';
import { useCallback } from 'react';
import { pluginApi, dashboardApi, userApi } from '@/lib/services/api';
import { ApiResponse, PluginState, PluginData, PluginConfig } from '@/lib/types/plugin';
import { DashboardConfig, UserConfig } from '@/lib/types/dashboard';

// 默认SWR配置
export const defaultSWRConfig: SWRConfiguration = {
  refreshInterval: 5000, // 5秒自动刷新
  revalidateOnFocus: true,
  revalidateOnReconnect: true,
  dedupingInterval: 2000,
  errorRetryCount: 3,
  errorRetryInterval: 1000,
  onError: (error) => {
    console.error('SWR Error:', error);
  },
};

// 通用fetcher函数
const fetcher = async (url: string) => {
  const response = await fetch(url);
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  const data = await response.json();
  if (!data.success) {
    throw new Error(data.error || 'API request failed');
  }
  return data.data;
};

// 插件状态数据Hook
export const usePluginStatus = (options?: SWRConfiguration) => {
  const { data, error, isLoading, mutate: revalidate } = useSWR(
    '/api/plugin/status',
    fetcher,
    {
      ...defaultSWRConfig,
      ...options,
    }
  );

  const refresh = useCallback(() => {
    revalidate();
  }, [revalidate]);

  return {
    plugins: data as PluginState[] | undefined,
    error,
    isLoading,
    refresh,
  };
};

// 插件配置数据Hook
export const usePluginConfigs = (options?: SWRConfiguration) => {
  const { data, error, isLoading, mutate: revalidate } = useSWR(
    '/api/plugin/config',
    fetcher,
    {
      ...defaultSWRConfig,
      refreshInterval: 30000, // 配置数据刷新频率较低
      ...options,
    }
  );

  const refresh = useCallback(() => {
    revalidate();
  }, [revalidate]);

  return {
    configs: data as PluginConfig[] | undefined,
    error,
    isLoading,
    refresh,
  };
};

// 插件数据Hook
export const usePluginData = (
  pluginId?: string,
  options?: SWRConfiguration
) => {
  const key = pluginId ? `/api/plugin/data/${pluginId}` : '/api/plugin/data';
  
  const { data, error, isLoading, mutate: revalidate } = useSWR(
    key,
    fetcher,
    {
      ...defaultSWRConfig,
      refreshInterval: 2000, // 数据刷新频率较高
      ...options,
    }
  );

  const refresh = useCallback(() => {
    revalidate();
  }, [revalidate]);

  return {
    data: data as PluginData[] | undefined,
    error,
    isLoading,
    refresh,
  };
};

// 仪表板配置Hook
export const useDashboards = (options?: SWRConfiguration) => {
  const { data, error, isLoading, mutate: revalidate } = useSWR(
    '/api/config/dashboard',
    fetcher,
    {
      ...defaultSWRConfig,
      refreshInterval: 0, // 仪表板配置不自动刷新
      ...options,
    }
  );

  const refresh = useCallback(() => {
    revalidate();
  }, [revalidate]);

  return {
    dashboards: data as DashboardConfig[] | undefined,
    error,
    isLoading,
    refresh,
  };
};

// 用户配置Hook
export const useUserConfig = (options?: SWRConfiguration) => {
  const { data, error, isLoading, mutate: revalidate } = useSWR(
    '/api/config/user',
    fetcher,
    {
      ...defaultSWRConfig,
      refreshInterval: 0, // 用户配置不自动刷新
      ...options,
    }
  );

  const refresh = useCallback(() => {
    revalidate();
  }, [revalidate]);

  return {
    userConfig: data as UserConfig | undefined,
    error,
    isLoading,
    refresh,
  };
};

// WebSocket状态Hook
export const useWebSocketStatus = (options?: SWRConfiguration) => {
  const { data, error, isLoading, mutate: revalidate } = useSWR(
    '/api/ws/status',
    fetcher,
    {
      ...defaultSWRConfig,
      refreshInterval: 10000, // 10秒检查一次WebSocket状态
      ...options,
    }
  );

  const refresh = useCallback(() => {
    revalidate();
  }, [revalidate]);

  return {
    status: data as { connected: boolean; uptime: number; connections: number } | undefined,
    error,
    isLoading,
    refresh,
  };
};

// 数据变更工具函数
export const mutatePluginStatus = (data?: PluginState[]) => {
  return mutate('/api/plugin/status', data, false);
};

export const mutatePluginConfigs = (data?: PluginConfig[]) => {
  return mutate('/api/plugin/config', data, false);
};

export const mutatePluginData = (pluginId?: string, data?: PluginData[]) => {
  const key = pluginId ? `/api/plugin/data/${pluginId}` : '/api/plugin/data';
  return mutate(key, data, false);
};

export const mutateDashboards = (data?: DashboardConfig[]) => {
  return mutate('/api/config/dashboard', data, false);
};

export const mutateUserConfig = (data?: UserConfig) => {
  return mutate('/api/config/user', data, false);
};

// 预加载数据函数
export const preloadPluginStatus = () => {
  return mutate('/api/plugin/status', fetcher('/api/plugin/status'));
};

export const preloadPluginConfigs = () => {
  return mutate('/api/plugin/config', fetcher('/api/plugin/config'));
};

export const preloadDashboards = () => {
  return mutate('/api/config/dashboard', fetcher('/api/config/dashboard'));
};

// 批量预加载
export const preloadAllData = async () => {
  await Promise.all([
    preloadPluginStatus(),
    preloadPluginConfigs(),
    preloadDashboards(),
  ]);
};

// 清除所有缓存
export const clearAllCache = () => {
  mutate(() => true, undefined, false);
};

// 条件性数据获取Hook
export const useConditionalSWR = <T>(
  key: string | null,
  fetcher: (key: string) => Promise<T>,
  options?: SWRConfiguration
) => {
  return useSWR(key, fetcher, {
    ...defaultSWRConfig,
    ...options,
  });
};

// 分页数据Hook
export const usePaginatedData = <T>(
  baseKey: string,
  page: number = 1,
  limit: number = 10,
  options?: SWRConfiguration
) => {
  const key = `${baseKey}?page=${page}&limit=${limit}`;
  
  const { data, error, isLoading, mutate: revalidate } = useSWR(
    key,
    fetcher,
    {
      ...defaultSWRConfig,
      ...options,
    }
  );

  const refresh = useCallback(() => {
    revalidate();
  }, [revalidate]);

  return {
    data: data as T | undefined,
    error,
    isLoading,
    refresh,
  };
};
