import { useEffect, useCallback, useRef } from 'react';
import { useWebSocket } from '@/lib/services/websocket';
import { 
  mutatePluginStatus, 
  mutatePluginData, 
  mutatePluginConfigs 
} from '@/lib/hooks/use-swr-config';
import { PluginState, PluginData, PluginConfig } from '@/lib/types/plugin';

interface RealTimeSyncOptions {
  enablePluginStatus?: boolean;
  enablePluginData?: boolean;
  enablePluginConfigs?: boolean;
  pluginIds?: string[];
  onStatusUpdate?: (status: PluginState) => void;
  onDataUpdate?: (data: PluginData) => void;
  onConfigUpdate?: (config: PluginConfig) => void;
  onConnectionChange?: (connected: boolean) => void;
  onError?: (error: any) => void;
}

export const useRealTimeSync = (options: RealTimeSyncOptions = {}) => {
  const {
    enablePluginStatus = true,
    enablePluginData = true,
    enablePluginConfigs = false,
    pluginIds,
    onStatusUpdate,
    onDataUpdate,
    onConfigUpdate,
    onConnectionChange,
    onError,
  } = options;

  const { 
    connected, 
    subscribe, 
    subscribeToPluginStatus, 
    subscribeToPluginData,
    unsubscribeFromPluginStatus,
    unsubscribeFromPluginData,
  } = useWebSocket();

  const subscriptionsRef = useRef<Array<() => void>>([]);

  // 处理插件状态更新
  const handleStatusUpdate = useCallback((status: PluginState) => {
    // 更新SWR缓存
    mutatePluginStatus((currentData) => {
      if (!currentData) return [status];
      
      const index = currentData.findIndex(p => p.plugin_id === status.plugin_id);
      if (index >= 0) {
        const newData = [...currentData];
        newData[index] = status;
        return newData;
      } else {
        return [...currentData, status];
      }
    });

    // 调用回调函数
    onStatusUpdate?.(status);
  }, [onStatusUpdate]);

  // 处理插件数据更新
  const handleDataUpdate = useCallback((data: PluginData) => {
    // 更新SWR缓存
    mutatePluginData(data.plugin_id, (currentData) => {
      if (!currentData) return [data];
      
      const newData = [...currentData, data];
      // 保持最新的100条数据
      return newData.slice(-100);
    });

    // 调用回调函数
    onDataUpdate?.(data);
  }, [onDataUpdate]);

  // 处理插件配置更新
  const handleConfigUpdate = useCallback((config: PluginConfig) => {
    // 更新SWR缓存
    mutatePluginConfigs((currentData) => {
      if (!currentData) return [config];
      
      const index = currentData.findIndex(c => c.id === config.id);
      if (index >= 0) {
        const newData = [...currentData];
        newData[index] = config;
        return newData;
      } else {
        return [...currentData, config];
      }
    });

    // 调用回调函数
    onConfigUpdate?.(config);
  }, [onConfigUpdate]);

  // 处理连接状态变化
  const handleConnectionChange = useCallback((connectionData: any) => {
    const isConnected = connectionData.status === 'connected';
    onConnectionChange?.(isConnected);

    if (isConnected) {
      // 重新订阅数据
      if (enablePluginStatus) {
        subscribeToPluginStatus(pluginIds);
      }
      if (enablePluginData) {
        subscribeToPluginData(pluginIds);
      }
    }
  }, [
    enablePluginStatus, 
    enablePluginData, 
    pluginIds, 
    subscribeToPluginStatus, 
    subscribeToPluginData,
    onConnectionChange
  ]);

  // 处理错误
  const handleError = useCallback((error: any) => {
    console.error('Real-time sync error:', error);
    onError?.(error);
  }, [onError]);

  // 设置订阅
  useEffect(() => {
    if (!connected) return;

    const subscriptions: Array<() => void> = [];

    try {
      // 订阅插件状态更新
      if (enablePluginStatus) {
        const unsubscribeStatus = subscribe('plugin_status', handleStatusUpdate);
        subscriptions.push(unsubscribeStatus);
        subscribeToPluginStatus(pluginIds);
      }

      // 订阅插件数据更新
      if (enablePluginData) {
        const unsubscribeData = subscribe('plugin_data', handleDataUpdate);
        subscriptions.push(unsubscribeData);
        subscribeToPluginData(pluginIds);
      }

      // 订阅插件配置更新
      if (enablePluginConfigs) {
        const unsubscribeConfig = subscribe('plugin_config', handleConfigUpdate);
        subscriptions.push(unsubscribeConfig);
      }

      // 订阅连接状态变化
      const unsubscribeConnection = subscribe('connection', handleConnectionChange);
      subscriptions.push(unsubscribeConnection);

      // 订阅错误事件
      const unsubscribeError = subscribe('error', handleError);
      subscriptions.push(unsubscribeError);

      subscriptionsRef.current = subscriptions;

    } catch (error) {
      handleError(error);
    }

    return () => {
      // 清理订阅
      subscriptions.forEach(unsubscribe => unsubscribe());
      subscriptionsRef.current = [];

      // 取消WebSocket订阅
      if (enablePluginStatus) {
        unsubscribeFromPluginStatus(pluginIds);
      }
      if (enablePluginData) {
        unsubscribeFromPluginData(pluginIds);
      }
    };
  }, [
    connected,
    enablePluginStatus,
    enablePluginData,
    enablePluginConfigs,
    pluginIds,
    subscribe,
    subscribeToPluginStatus,
    subscribeToPluginData,
    unsubscribeFromPluginStatus,
    unsubscribeFromPluginData,
    handleStatusUpdate,
    handleDataUpdate,
    handleConfigUpdate,
    handleConnectionChange,
    handleError,
  ]);

  // 手动刷新订阅
  const refreshSubscriptions = useCallback(() => {
    if (connected) {
      if (enablePluginStatus) {
        subscribeToPluginStatus(pluginIds);
      }
      if (enablePluginData) {
        subscribeToPluginData(pluginIds);
      }
    }
  }, [connected, enablePluginStatus, enablePluginData, pluginIds, subscribeToPluginStatus, subscribeToPluginData]);

  return {
    connected,
    refreshSubscriptions,
  };
};

// 专门用于插件监控页面的Hook
export const usePluginMonitorSync = (pluginIds?: string[]) => {
  return useRealTimeSync({
    enablePluginStatus: true,
    enablePluginData: true,
    enablePluginConfigs: false,
    pluginIds,
  });
};

// 专门用于仪表板页面的Hook
export const useDashboardSync = () => {
  return useRealTimeSync({
    enablePluginStatus: true,
    enablePluginData: true,
    enablePluginConfigs: false,
  });
};

// 专门用于HMI页面的Hook
export const useHMISync = (pluginIds?: string[]) => {
  return useRealTimeSync({
    enablePluginStatus: false,
    enablePluginData: true,
    enablePluginConfigs: false,
    pluginIds,
  });
};

// 批量数据同步Hook
export const useBatchSync = (options: {
  batchSize?: number;
  batchInterval?: number;
  onBatchUpdate?: (updates: any[]) => void;
}) => {
  const { batchSize = 10, batchInterval = 1000, onBatchUpdate } = options;
  const batchRef = useRef<any[]>([]);
  const timerRef = useRef<NodeJS.Timeout>();

  const addToBatch = useCallback((update: any) => {
    batchRef.current.push(update);

    if (batchRef.current.length >= batchSize) {
      // 立即处理批次
      onBatchUpdate?.(batchRef.current);
      batchRef.current = [];
      
      if (timerRef.current) {
        clearTimeout(timerRef.current);
        timerRef.current = undefined;
      }
    } else if (!timerRef.current) {
      // 设置定时器
      timerRef.current = setTimeout(() => {
        if (batchRef.current.length > 0) {
          onBatchUpdate?.(batchRef.current);
          batchRef.current = [];
        }
        timerRef.current = undefined;
      }, batchInterval);
    }
  }, [batchSize, batchInterval, onBatchUpdate]);

  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, []);

  return { addToBatch };
};
