import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { DashboardConfig, UserConfig } from '@/lib/types/dashboard';
import { dashboardApi, userApi } from '@/lib/services/api';

interface ConfigStore {
  // 用户配置
  userConfig: UserConfig;
  
  // 仪表板配置
  dashboards: DashboardConfig[];
  currentDashboardId: string | null;
  
  // 主题配置
  theme: 'light' | 'dark' | 'auto';
  customTheme: Record<string, any>;
  
  // 布局配置
  sidebarCollapsed: boolean;
  sidebarWidth: number;
  
  // 加载状态
  loading: boolean;
  error: string | null;

  // Actions
  // 用户配置相关
  updateUserConfig: (config: Partial<UserConfig>) => void;
  loadUserConfig: () => Promise<void>;
  saveUserConfig: () => Promise<void>;
  resetUserConfig: () => void;

  // 仪表板配置相关
  loadDashboards: () => Promise<void>;
  createDashboard: (config: Omit<DashboardConfig, 'id' | 'created_at' | 'updated_at'>) => Promise<void>;
  updateDashboard: (id: string, config: Partial<DashboardConfig>) => Promise<void>;
  deleteDashboard: (id: string) => Promise<void>;
  setCurrentDashboard: (id: string | null) => void;
  duplicateDashboard: (id: string) => Promise<void>;

  // 主题相关
  setTheme: (theme: 'light' | 'dark' | 'auto') => void;
  updateCustomTheme: (theme: Record<string, any>) => void;
  applyTheme: () => void;

  // 布局相关
  setSidebarCollapsed: (collapsed: boolean) => void;
  setSidebarWidth: (width: number) => void;

  // 导入导出
  exportConfig: () => string;
  importConfig: (configJson: string) => Promise<void>;

  // 错误处理
  clearError: () => void;
}

// 默认用户配置
const defaultUserConfig: UserConfig = {
  theme: 'light',
  language: 'zh-CN',
  timezone: 'Asia/Shanghai',
  dateFormat: 'YYYY-MM-DD HH:mm:ss',
  numberFormat: 'en-US',
  sidebarCollapsed: false,
  refreshInterval: 5000,
  notifications: {
    enabled: true,
    sound: true,
    desktop: false,
    email: false,
  },
};

export const useConfigStore = create<ConfigStore>()(
  persist(
    (set, get) => ({
      // 初始状态
      userConfig: defaultUserConfig,
      dashboards: [],
      currentDashboardId: null,
      theme: 'light',
      customTheme: {},
      sidebarCollapsed: false,
      sidebarWidth: 240,
      loading: false,
      error: null,

      // 用户配置相关
      updateUserConfig: (config) => {
        set((state) => ({
          userConfig: { ...state.userConfig, ...config },
        }));
      },

      loadUserConfig: async () => {
        set({ loading: true, error: null });
        try {
          const response = await userApi.getUserConfig();
          if (response.success && response.data) {
            set({ 
              userConfig: { ...defaultUserConfig, ...response.data },
              loading: false 
            });
          } else {
            set({ error: response.error || '加载用户配置失败', loading: false });
          }
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : '网络错误', 
            loading: false 
          });
        }
      },

      saveUserConfig: async () => {
        const { userConfig } = get();
        set({ loading: true, error: null });
        try {
          const response = await userApi.updateUserConfig(userConfig);
          if (response.success) {
            set({ loading: false });
          } else {
            set({ error: response.error || '保存用户配置失败', loading: false });
          }
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : '网络错误', 
            loading: false 
          });
        }
      },

      resetUserConfig: () => {
        set({ userConfig: defaultUserConfig });
      },

      // 仪表板配置相关
      loadDashboards: async () => {
        set({ loading: true, error: null });
        try {
          const response = await dashboardApi.getDashboards();
          if (response.success && response.data) {
            set({ 
              dashboards: response.data,
              loading: false 
            });
          } else {
            set({ error: response.error || '加载仪表板失败', loading: false });
          }
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : '网络错误', 
            loading: false 
          });
        }
      },

      createDashboard: async (config) => {
        set({ loading: true, error: null });
        try {
          const response = await dashboardApi.createDashboard(config);
          if (response.success && response.data) {
            set((state) => ({
              dashboards: [...state.dashboards, response.data!],
              currentDashboardId: response.data!.id,
              loading: false,
            }));
          } else {
            set({ error: response.error || '创建仪表板失败', loading: false });
          }
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : '网络错误', 
            loading: false 
          });
        }
      },

      updateDashboard: async (id, config) => {
        set({ loading: true, error: null });
        try {
          const response = await dashboardApi.updateDashboard(id, config);
          if (response.success && response.data) {
            set((state) => ({
              dashboards: state.dashboards.map(d => 
                d.id === id ? response.data! : d
              ),
              loading: false,
            }));
          } else {
            set({ error: response.error || '更新仪表板失败', loading: false });
          }
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : '网络错误', 
            loading: false 
          });
        }
      },

      deleteDashboard: async (id) => {
        set({ loading: true, error: null });
        try {
          const response = await dashboardApi.deleteDashboard(id);
          if (response.success) {
            set((state) => ({
              dashboards: state.dashboards.filter(d => d.id !== id),
              currentDashboardId: state.currentDashboardId === id ? null : state.currentDashboardId,
              loading: false,
            }));
          } else {
            set({ error: response.error || '删除仪表板失败', loading: false });
          }
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : '网络错误', 
            loading: false 
          });
        }
      },

      setCurrentDashboard: (id) => {
        set({ currentDashboardId: id });
      },

      duplicateDashboard: async (id) => {
        const { dashboards } = get();
        const dashboard = dashboards.find(d => d.id === id);
        if (!dashboard) return;

        const newConfig = {
          ...dashboard,
          name: `${dashboard.name} (副本)`,
          id: undefined,
          created_at: undefined,
          updated_at: undefined,
          created_by: undefined,
        };

        await get().createDashboard(newConfig);
      },

      // 主题相关
      setTheme: (theme) => {
        set({ theme });
        get().applyTheme();
      },

      updateCustomTheme: (theme) => {
        set({ customTheme: theme });
        get().applyTheme();
      },

      applyTheme: () => {
        const { theme, customTheme } = get();
        const root = document.documentElement;
        
        // 应用主题类
        root.className = root.className.replace(/theme-\w+/g, '');
        root.classList.add(`theme-${theme}`);
        
        // 应用自定义主题变量
        Object.entries(customTheme).forEach(([key, value]) => {
          root.style.setProperty(`--${key}`, value as string);
        });
      },

      // 布局相关
      setSidebarCollapsed: (collapsed) => {
        set({ sidebarCollapsed: collapsed });
      },

      setSidebarWidth: (width) => {
        set({ sidebarWidth: width });
      },

      // 导入导出
      exportConfig: () => {
        const { userConfig, dashboards, customTheme } = get();
        const config = {
          userConfig,
          dashboards,
          customTheme,
          exportTime: new Date().toISOString(),
          version: '1.0.0',
        };
        return JSON.stringify(config, null, 2);
      },

      importConfig: async (configJson) => {
        try {
          const config = JSON.parse(configJson);
          
          if (config.userConfig) {
            set({ userConfig: { ...defaultUserConfig, ...config.userConfig } });
          }
          
          if (config.customTheme) {
            set({ customTheme: config.customTheme });
          }
          
          if (config.dashboards && Array.isArray(config.dashboards)) {
            // 导入仪表板时生成新的ID
            const importedDashboards = config.dashboards.map((dashboard: any) => ({
              ...dashboard,
              id: `imported-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
              name: `${dashboard.name} (导入)`,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            }));
            
            set((state) => ({
              dashboards: [...state.dashboards, ...importedDashboards],
            }));
          }
          
          get().applyTheme();
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : '配置文件格式错误' 
          });
          throw error;
        }
      },

      // 错误处理
      clearError: () => {
        set({ error: null });
      },
    }),
    {
      name: 'config-store',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        userConfig: state.userConfig,
        theme: state.theme,
        customTheme: state.customTheme,
        sidebarCollapsed: state.sidebarCollapsed,
        sidebarWidth: state.sidebarWidth,
        currentDashboardId: state.currentDashboardId,
      }),
    }
  )
);
