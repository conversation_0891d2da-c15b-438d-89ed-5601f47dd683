import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { PluginState, PluginData, PluginConfig, PluginStatus } from '@/lib/types/plugin';
import { pluginApi } from '@/lib/services/api';

interface PluginStore {
  // 状态
  plugins: Record<string, PluginState>;
  pluginData: Record<string, PluginData[]>;
  pluginConfigs: Record<string, PluginConfig>;
  loading: boolean;
  error: string | null;
  lastUpdated: number;

  // 统计信息
  stats: {
    total: number;
    running: number;
    error: number;
    idle: number;
    stopped: number;
  };

  // Actions
  fetchPluginStatus: () => Promise<void>;
  fetchPluginData: (pluginId?: string) => Promise<void>;
  fetchPluginConfigs: () => Promise<void>;
  updatePluginStatus: (pluginId: string, status: PluginState) => void;
  addPluginData: (pluginId: string, data: PluginData) => void;
  executePluginAction: (pluginId: string, action: string, payload?: any) => Promise<void>;
  clearError: () => void;
  reset: () => void;
}

const initialState = {
  plugins: {},
  pluginData: {},
  pluginConfigs: {},
  loading: false,
  error: null,
  lastUpdated: 0,
  stats: {
    total: 0,
    running: 0,
    error: 0,
    idle: 0,
    stopped: 0,
  },
};

export const usePluginStore = create<PluginStore>()(
  devtools(
    (set, get) => ({
      ...initialState,

      fetchPluginStatus: async () => {
        set({ loading: true, error: null });
        try {
          const response = await pluginApi.getPluginStatus();
          if (response.success && response.data) {
            const plugins: Record<string, PluginState> = {};
            const stats = {
              total: response.data.length,
              running: 0,
              error: 0,
              idle: 0,
              stopped: 0,
            };

            response.data.forEach((plugin) => {
              plugins[plugin.plugin_id] = plugin;
              
              // 更新统计信息
              switch (plugin.status) {
                case PluginStatus.RUNNING:
                  stats.running++;
                  break;
                case PluginStatus.ERROR:
                  stats.error++;
                  break;
                case PluginStatus.IDLE:
                  stats.idle++;
                  break;
                case PluginStatus.STOPPED:
                  stats.stopped++;
                  break;
              }
            });

            set({
              plugins,
              stats,
              loading: false,
              lastUpdated: Date.now(),
            });
          } else {
            set({ error: response.error || '获取插件状态失败', loading: false });
          }
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : '网络错误', 
            loading: false 
          });
        }
      },

      fetchPluginData: async (pluginId?: string) => {
        try {
          const response = pluginId 
            ? await pluginApi.getPluginDataById(pluginId)
            : await pluginApi.getPluginData();

          if (response.success && response.data) {
            const { pluginData } = get();
            const newPluginData = { ...pluginData };

            if (pluginId) {
              // 单个插件数据
              newPluginData[pluginId] = Array.isArray(response.data) 
                ? response.data 
                : [response.data];
            } else {
              // 所有插件数据
              const dataArray = Array.isArray(response.data) ? response.data : [response.data];
              dataArray.forEach((data) => {
                if (!newPluginData[data.plugin_id]) {
                  newPluginData[data.plugin_id] = [];
                }
                newPluginData[data.plugin_id].push(data);
                
                // 保持最新的100条数据
                if (newPluginData[data.plugin_id].length > 100) {
                  newPluginData[data.plugin_id] = newPluginData[data.plugin_id].slice(-100);
                }
              });
            }

            set({ pluginData: newPluginData });
          }
        } catch (error) {
          console.error('获取插件数据失败:', error);
        }
      },

      fetchPluginConfigs: async () => {
        try {
          const response = await pluginApi.getPluginConfigs();
          if (response.success && response.data) {
            const configs: Record<string, PluginConfig> = {};
            response.data.forEach((config) => {
              configs[config.id] = config;
            });
            set({ pluginConfigs: configs });
          }
        } catch (error) {
          console.error('获取插件配置失败:', error);
        }
      },

      updatePluginStatus: (pluginId: string, status: PluginState) => {
        const { plugins, stats } = get();
        const oldStatus = plugins[pluginId]?.status;
        
        // 更新插件状态
        const newPlugins = {
          ...plugins,
          [pluginId]: status,
        };

        // 更新统计信息
        const newStats = { ...stats };
        
        // 减少旧状态计数
        if (oldStatus) {
          switch (oldStatus) {
            case PluginStatus.RUNNING:
              newStats.running--;
              break;
            case PluginStatus.ERROR:
              newStats.error--;
              break;
            case PluginStatus.IDLE:
              newStats.idle--;
              break;
            case PluginStatus.STOPPED:
              newStats.stopped--;
              break;
          }
        } else {
          newStats.total++;
        }

        // 增加新状态计数
        switch (status.status) {
          case PluginStatus.RUNNING:
            newStats.running++;
            break;
          case PluginStatus.ERROR:
            newStats.error++;
            break;
          case PluginStatus.IDLE:
            newStats.idle++;
            break;
          case PluginStatus.STOPPED:
            newStats.stopped++;
            break;
        }

        set({
          plugins: newPlugins,
          stats: newStats,
          lastUpdated: Date.now(),
        });
      },

      addPluginData: (pluginId: string, data: PluginData) => {
        const { pluginData } = get();
        const currentData = pluginData[pluginId] || [];
        const newData = [...currentData, data];
        
        // 保持最新的100条数据
        if (newData.length > 100) {
          newData.splice(0, newData.length - 100);
        }

        set({
          pluginData: {
            ...pluginData,
            [pluginId]: newData,
          },
        });
      },

      executePluginAction: async (pluginId: string, action: string, payload?: any) => {
        try {
          const response = await pluginApi.executePluginAction(pluginId, action, payload);
          if (!response.success) {
            throw new Error(response.error || '执行插件操作失败');
          }
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : '执行插件操作失败' 
          });
          throw error;
        }
      },

      clearError: () => set({ error: null }),

      reset: () => set(initialState),
    }),
    {
      name: 'plugin-store',
    }
  )
);
