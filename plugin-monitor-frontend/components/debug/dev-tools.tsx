'use client';

import React, { useState, useEffect } from 'react';
import { 
  Drawer, 
  Tabs, 
  Card, 
  Button, 
  Switch, 
  Input, 
  Select, 
  Space,
  Table,
  Tag,
  message,
  Collapse,
  Typography
} from 'antd';
import {
  BugOutlined,
  SettingOutlined,
  DatabaseOutlined,
  ApiOutlined,
  MonitorOutlined,
  ClearOutlined,
  DownloadOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import { useConfigStore } from '@/lib/stores/config-store';
import { usePluginStore } from '@/lib/stores/plugin-store';
import { useWebSocket } from '@/lib/services/websocket';

const { TabPane } = Tabs;
const { Panel } = Collapse;
const { TextArea } = Input;
const { Option } = Select;
const { Text } = Typography;

interface DevToolsProps {
  visible: boolean;
  onClose: () => void;
}

export const DevTools: React.FC<DevToolsProps> = ({ visible, onClose }) => {
  const [activeTab, setActiveTab] = useState('state');
  const [mockDataEnabled, setMockDataEnabled] = useState(false);
  const [apiLogs, setApiLogs] = useState<any[]>([]);
  const [consoleOutput, setConsoleOutput] = useState<string[]>([]);

  const configStore = useConfigStore();
  const pluginStore = usePluginStore();
  const { connected, send } = useWebSocket();

  // 拦截console.log
  useEffect(() => {
    const originalLog = console.log;
    const originalError = console.error;
    const originalWarn = console.warn;

    console.log = (...args) => {
      setConsoleOutput(prev => [...prev.slice(-100), `[LOG] ${args.join(' ')}`]);
      originalLog(...args);
    };

    console.error = (...args) => {
      setConsoleOutput(prev => [...prev.slice(-100), `[ERROR] ${args.join(' ')}`]);
      originalError(...args);
    };

    console.warn = (...args) => {
      setConsoleOutput(prev => [...prev.slice(-100), `[WARN] ${args.join(' ')}`]);
      originalWarn(...args);
    };

    return () => {
      console.log = originalLog;
      console.error = originalError;
      console.warn = originalWarn;
    };
  }, []);

  // 状态管理标签页
  const StateTab = () => (
    <div className="space-y-4">
      <Card title="配置状态" size="small">
        <Collapse ghost>
          <Panel header="用户配置" key="userConfig">
            <pre className="text-xs bg-gray-50 p-2 rounded overflow-auto max-h-40">
              {JSON.stringify(configStore.userConfig, null, 2)}
            </pre>
          </Panel>
          <Panel header="仪表板配置" key="dashboards">
            <pre className="text-xs bg-gray-50 p-2 rounded overflow-auto max-h-40">
              {JSON.stringify(configStore.dashboards, null, 2)}
            </pre>
          </Panel>
        </Collapse>
      </Card>

      <Card title="插件状态" size="small">
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>插件总数:</span>
            <span>{Object.keys(pluginStore.plugins).length}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span>运行中:</span>
            <span>{pluginStore.stats.running}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span>错误:</span>
            <span>{pluginStore.stats.error}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span>最后更新:</span>
            <span>{new Date(pluginStore.lastUpdated).toLocaleTimeString()}</span>
          </div>
        </div>
      </Card>

      <Card title="操作" size="small">
        <Space direction="vertical" className="w-full">
          <Button 
            block 
            onClick={() => {
              localStorage.clear();
              sessionStorage.clear();
              message.success('存储已清除');
            }}
          >
            清除本地存储
          </Button>
          <Button 
            block 
            onClick={() => {
              pluginStore.reset();
              configStore.clearError();
              message.success('状态已重置');
            }}
          >
            重置应用状态
          </Button>
        </Space>
      </Card>
    </div>
  );

  // API调试标签页
  const ApiTab = () => (
    <div className="space-y-4">
      <Card title="WebSocket状态" size="small">
        <div className="space-y-2">
          <div className="flex justify-between">
            <span>连接状态:</span>
            <Tag color={connected ? 'green' : 'red'}>
              {connected ? '已连接' : '未连接'}
            </Tag>
          </div>
          <Button 
            size="small" 
            onClick={() => send('ping', { timestamp: Date.now() })}
            disabled={!connected}
          >
            发送Ping
          </Button>
        </div>
      </Card>

      <Card title="模拟数据" size="small">
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span>启用模拟数据:</span>
            <Switch 
              checked={mockDataEnabled}
              onChange={setMockDataEnabled}
            />
          </div>
          <Button 
            size="small" 
            block
            onClick={() => {
              // 生成模拟插件状态
              const mockStatus = {
                plugin_id: `mock-${Date.now()}`,
                plugin_type: 'inbound',
                status: 'running',
                timestamp: Date.now(),
                details: {
                  memory_usage: `${Math.random() * 100}MB`,
                  cpu_usage: `${Math.random() * 100}%`,
                },
              };
              pluginStore.updatePluginStatus(mockStatus.plugin_id, mockStatus);
              message.success('已生成模拟数据');
            }}
          >
            生成模拟插件状态
          </Button>
        </div>
      </Card>

      <Card title="API日志" size="small">
        <div className="max-h-40 overflow-auto">
          {apiLogs.length === 0 ? (
            <Text type="secondary">暂无API调用记录</Text>
          ) : (
            apiLogs.map((log, index) => (
              <div key={index} className="text-xs border-b py-1">
                <div className="flex justify-between">
                  <span>{log.method} {log.url}</span>
                  <span>{log.status}</span>
                </div>
                <div className="text-gray-500">{log.timestamp}</div>
              </div>
            ))
          )}
        </div>
        <Button 
          size="small" 
          icon={<ClearOutlined />}
          onClick={() => setApiLogs([])}
          className="mt-2"
        >
          清除日志
        </Button>
      </Card>
    </div>
  );

  // 性能监控标签页
  const PerformanceTab = () => (
    <div className="space-y-4">
      <Card title="内存使用" size="small">
        {(performance as any).memory ? (
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span>已使用:</span>
              <span>{Math.round((performance as any).memory.usedJSHeapSize / 1024 / 1024)}MB</span>
            </div>
            <div className="flex justify-between">
              <span>总计:</span>
              <span>{Math.round((performance as any).memory.totalJSHeapSize / 1024 / 1024)}MB</span>
            </div>
            <div className="flex justify-between">
              <span>限制:</span>
              <span>{Math.round((performance as any).memory.jsHeapSizeLimit / 1024 / 1024)}MB</span>
            </div>
          </div>
        ) : (
          <Text type="secondary">内存信息不可用</Text>
        )}
      </Card>

      <Card title="页面性能" size="small">
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span>DOM节点:</span>
            <span>{document.querySelectorAll('*').length}</span>
          </div>
          <div className="flex justify-between">
            <span>事件监听器:</span>
            <span>{(window as any).getEventListeners ? 'Available' : 'N/A'}</span>
          </div>
        </div>
      </Card>

      <Card title="网络信息" size="small">
        {(navigator as any).connection ? (
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span>连接类型:</span>
              <span>{(navigator as any).connection.effectiveType}</span>
            </div>
            <div className="flex justify-between">
              <span>下行速度:</span>
              <span>{(navigator as any).connection.downlink}Mbps</span>
            </div>
          </div>
        ) : (
          <Text type="secondary">网络信息不可用</Text>
        )}
      </Card>
    </div>
  );

  // 控制台标签页
  const ConsoleTab = () => (
    <div className="space-y-4">
      <Card title="控制台输出" size="small">
        <div className="bg-black text-green-400 p-2 rounded font-mono text-xs max-h-60 overflow-auto">
          {consoleOutput.length === 0 ? (
            <div className="text-gray-500">暂无输出</div>
          ) : (
            consoleOutput.map((line, index) => (
              <div key={index}>{line}</div>
            ))
          )}
        </div>
        <div className="mt-2 flex space-x-2">
          <Button 
            size="small" 
            icon={<ClearOutlined />}
            onClick={() => setConsoleOutput([])}
          >
            清除
          </Button>
          <Button 
            size="small" 
            icon={<DownloadOutlined />}
            onClick={() => {
              const content = consoleOutput.join('\n');
              const blob = new Blob([content], { type: 'text/plain' });
              const url = URL.createObjectURL(blob);
              const a = document.createElement('a');
              a.href = url;
              a.download = `console-${Date.now()}.log`;
              a.click();
              URL.revokeObjectURL(url);
            }}
          >
            导出
          </Button>
        </div>
      </Card>

      <Card title="执行代码" size="small">
        <TextArea
          placeholder="输入JavaScript代码..."
          rows={4}
          onPressEnter={(e) => {
            if (e.ctrlKey || e.metaKey) {
              try {
                const result = eval((e.target as HTMLTextAreaElement).value);
                console.log('执行结果:', result);
                message.success('代码执行成功');
              } catch (error) {
                console.error('执行错误:', error);
                message.error('代码执行失败');
              }
            }
          }}
        />
        <div className="mt-2 text-xs text-gray-500">
          按 Ctrl+Enter 执行代码
        </div>
      </Card>
    </div>
  );

  return (
    <Drawer
      title="开发工具"
      placement="right"
      width={400}
      open={visible}
      onClose={onClose}
      extra={
        <Tag color="orange">DEV</Tag>
      }
    >
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane
          tab={
            <span>
              <DatabaseOutlined />
              状态
            </span>
          }
          key="state"
        >
          <StateTab />
        </TabPane>

        <TabPane
          tab={
            <span>
              <ApiOutlined />
              API
            </span>
          }
          key="api"
        >
          <ApiTab />
        </TabPane>

        <TabPane
          tab={
            <span>
              <MonitorOutlined />
              性能
            </span>
          }
          key="performance"
        >
          <PerformanceTab />
        </TabPane>

        <TabPane
          tab={
            <span>
              <BugOutlined />
              控制台
            </span>
          }
          key="console"
        >
          <ConsoleTab />
        </TabPane>
      </Tabs>
    </Drawer>
  );
};

// 开发工具触发器
export const DevToolsTrigger: React.FC = () => {
  const [visible, setVisible] = useState(false);

  // 只在开发环境显示
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <>
      <Button
        type="text"
        icon={<SettingOutlined />}
        onClick={() => setVisible(true)}
        className="fixed bottom-4 left-4 z-50 shadow-lg"
        title="开发工具"
      />
      <DevTools visible={visible} onClose={() => setVisible(false)} />
    </>
  );
};
