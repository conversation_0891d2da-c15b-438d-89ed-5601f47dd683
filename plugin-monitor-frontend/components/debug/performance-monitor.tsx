'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Card, Statistic, Row, Col, Switch, Button, Drawer, Table } from 'antd';
import { 
  MonitorOutlined, 
  CloseOutlined, 
  ReloadOutlined,
  WarningOutlined 
} from '@ant-design/icons';

interface PerformanceMetric {
  name: string;
  value: number;
  unit: string;
  threshold?: number;
  timestamp: number;
}

interface RenderMetric {
  component: string;
  renderTime: number;
  timestamp: number;
}

export const PerformanceMonitor: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [isEnabled, setIsEnabled] = useState(false);
  const [metrics, setMetrics] = useState<PerformanceMetric[]>([]);
  const [renderMetrics, setRenderMetrics] = useState<RenderMetric[]>([]);
  const intervalRef = useRef<NodeJS.Timeout>();

  // 收集性能指标
  const collectMetrics = () => {
    if (!isEnabled) return;

    const now = Date.now();
    const newMetrics: PerformanceMetric[] = [];

    // 内存使用情况
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      newMetrics.push({
        name: 'Used JS Heap Size',
        value: Math.round(memory.usedJSHeapSize / 1024 / 1024),
        unit: 'MB',
        threshold: 100,
        timestamp: now,
      });
      newMetrics.push({
        name: 'Total JS Heap Size',
        value: Math.round(memory.totalJSHeapSize / 1024 / 1024),
        unit: 'MB',
        timestamp: now,
      });
    }

    // FPS (近似计算)
    if (window.requestAnimationFrame) {
      let fps = 0;
      let lastTime = performance.now();
      let frameCount = 0;

      const countFPS = () => {
        frameCount++;
        const currentTime = performance.now();
        if (currentTime >= lastTime + 1000) {
          fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
          frameCount = 0;
          lastTime = currentTime;
          
          newMetrics.push({
            name: 'FPS',
            value: fps,
            unit: '',
            threshold: 30,
            timestamp: now,
          });
        }
        if (isEnabled) {
          requestAnimationFrame(countFPS);
        }
      };
      requestAnimationFrame(countFPS);
    }

    // 网络连接信息
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      newMetrics.push({
        name: 'Network Speed',
        value: connection.downlink || 0,
        unit: 'Mbps',
        timestamp: now,
      });
    }

    // DOM 节点数量
    newMetrics.push({
      name: 'DOM Nodes',
      value: document.querySelectorAll('*').length,
      unit: '',
      threshold: 1000,
      timestamp: now,
    });

    setMetrics(prev => [...prev.slice(-50), ...newMetrics]);
  };

  // 启动/停止监控
  useEffect(() => {
    if (isEnabled) {
      intervalRef.current = setInterval(collectMetrics, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isEnabled]);

  // 清除数据
  const clearMetrics = () => {
    setMetrics([]);
    setRenderMetrics([]);
  };

  // 获取最新指标
  const getLatestMetric = (name: string) => {
    const metric = metrics.filter(m => m.name === name).slice(-1)[0];
    return metric || { value: 0, unit: '' };
  };

  // 检查是否超过阈值
  const isOverThreshold = (metric: PerformanceMetric) => {
    return metric.threshold && metric.value > metric.threshold;
  };

  // 表格列定义
  const columns = [
    {
      title: '指标名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '当前值',
      dataIndex: 'value',
      key: 'value',
      render: (value: number, record: PerformanceMetric) => (
        <span style={{ color: isOverThreshold(record) ? '#ff4d4f' : undefined }}>
          {value} {record.unit}
          {isOverThreshold(record) && <WarningOutlined className="ml-1" />}
        </span>
      ),
    },
    {
      title: '阈值',
      dataIndex: 'threshold',
      key: 'threshold',
      render: (threshold?: number, record: PerformanceMetric) => 
        threshold ? `${threshold} ${record.unit}` : '-',
    },
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      render: (timestamp: number) => new Date(timestamp).toLocaleTimeString(),
    },
  ];

  // 获取唯一的最新指标
  const latestMetrics = metrics.reduce((acc, metric) => {
    acc[metric.name] = metric;
    return acc;
  }, {} as Record<string, PerformanceMetric>);

  return (
    <>
      {/* 浮动监控按钮 */}
      <div 
        className="fixed bottom-4 right-4 z-50"
        style={{ display: process.env.NODE_ENV === 'development' ? 'block' : 'none' }}
      >
        <Button
          type="primary"
          shape="circle"
          icon={<MonitorOutlined />}
          size="large"
          onClick={() => setIsVisible(true)}
          className="shadow-lg"
        />
      </div>

      {/* 性能监控面板 */}
      <Drawer
        title={
          <div className="flex items-center justify-between">
            <span>性能监控</span>
            <div className="flex items-center space-x-2">
              <span className="text-sm">启用监控</span>
              <Switch
                checked={isEnabled}
                onChange={setIsEnabled}
                size="small"
              />
            </div>
          </div>
        }
        placement="right"
        width={600}
        open={isVisible}
        onClose={() => setIsVisible(false)}
        extra={
          <Button
            icon={<ReloadOutlined />}
            onClick={clearMetrics}
            size="small"
          >
            清除数据
          </Button>
        }
      >
        {isEnabled ? (
          <div className="space-y-4">
            {/* 关键指标卡片 */}
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Card size="small">
                  <Statistic
                    title="内存使用"
                    value={getLatestMetric('Used JS Heap Size').value}
                    suffix="MB"
                    valueStyle={{ 
                      color: getLatestMetric('Used JS Heap Size').value > 100 ? '#ff4d4f' : '#3f8600' 
                    }}
                  />
                </Card>
              </Col>
              <Col span={12}>
                <Card size="small">
                  <Statistic
                    title="帧率"
                    value={getLatestMetric('FPS').value}
                    suffix="FPS"
                    valueStyle={{ 
                      color: getLatestMetric('FPS').value < 30 ? '#ff4d4f' : '#3f8600' 
                    }}
                  />
                </Card>
              </Col>
              <Col span={12}>
                <Card size="small">
                  <Statistic
                    title="DOM节点"
                    value={getLatestMetric('DOM Nodes').value}
                    valueStyle={{ 
                      color: getLatestMetric('DOM Nodes').value > 1000 ? '#ff4d4f' : '#3f8600' 
                    }}
                  />
                </Card>
              </Col>
              <Col span={12}>
                <Card size="small">
                  <Statistic
                    title="网络速度"
                    value={getLatestMetric('Network Speed').value}
                    suffix="Mbps"
                  />
                </Card>
              </Col>
            </Row>

            {/* 详细指标表格 */}
            <Card title="详细指标" size="small">
              <Table
                columns={columns}
                dataSource={Object.values(latestMetrics)}
                rowKey="name"
                size="small"
                pagination={false}
              />
            </Card>

            {/* 性能建议 */}
            <Card title="性能建议" size="small">
              <div className="space-y-2 text-sm">
                {getLatestMetric('Used JS Heap Size').value > 100 && (
                  <div className="text-red-600">
                    ⚠️ 内存使用过高，建议检查内存泄漏
                  </div>
                )}
                {getLatestMetric('FPS').value < 30 && (
                  <div className="text-red-600">
                    ⚠️ 帧率过低，可能影响用户体验
                  </div>
                )}
                {getLatestMetric('DOM Nodes').value > 1000 && (
                  <div className="text-orange-600">
                    ⚠️ DOM节点过多，建议优化组件结构
                  </div>
                )}
                {Object.values(latestMetrics).every(m => !isOverThreshold(m)) && (
                  <div className="text-green-600">
                    ✅ 所有指标正常
                  </div>
                )}
              </div>
            </Card>
          </div>
        ) : (
          <div className="text-center text-gray-500 py-8">
            请启用监控以查看性能数据
          </div>
        )}
      </Drawer>
    </>
  );
};

// React 组件渲染时间监控 Hook
export const useRenderTime = (componentName: string) => {
  const startTime = useRef<number>();

  useEffect(() => {
    startTime.current = performance.now();
    
    return () => {
      if (startTime.current) {
        const renderTime = performance.now() - startTime.current;
        console.log(`${componentName} render time: ${renderTime.toFixed(2)}ms`);
      }
    };
  });
};
