'use client';

import React from 'react';
import { Result, Button, Collapse, Typography, Tag } from 'antd';
import { 
  ExceptionOutlined, 
  ReloadOutlined, 
  BugOutlined,
  InfoCircleOutlined,
  WarningOutlined 
} from '@ant-design/icons';

const { Panel } = Collapse;
const { Text, Paragraph } = Typography;

interface ErrorInfo {
  componentStack: string;
  errorBoundary?: string;
  errorBoundaryStack?: string;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
  errorId: string;
  timestamp: number;
}

interface EnhancedErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error: Error; retry: () => void; errorId: string }>;
  onError?: (error: Error, errorInfo: ErrorInfo, errorId: string) => void;
  showDetails?: boolean;
  level?: 'page' | 'section' | 'component';
}

export class EnhancedErrorBoundary extends React.Component<
  EnhancedErrorBoundaryProps,
  ErrorBoundaryState
> {
  constructor(props: EnhancedErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      errorId: '',
      timestamp: 0,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    const errorId = `error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    return {
      hasError: true,
      error,
      errorId,
      timestamp: Date.now(),
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    const enhancedErrorInfo: ErrorInfo = {
      componentStack: errorInfo.componentStack,
      errorBoundary: errorInfo.errorBoundary,
      errorBoundaryStack: errorInfo.errorBoundaryStack,
    };

    this.setState({
      errorInfo: enhancedErrorInfo,
    });

    // 记录错误到控制台
    console.group(`🚨 Error Boundary Caught Error [${this.state.errorId}]`);
    console.error('Error:', error);
    console.error('Error Info:', enhancedErrorInfo);
    console.error('Props:', this.props);
    console.groupEnd();

    // 发送错误报告
    this.reportError(error, enhancedErrorInfo);

    // 调用错误处理回调
    this.props.onError?.(error, enhancedErrorInfo, this.state.errorId);
  }

  reportError = (error: Error, errorInfo: ErrorInfo) => {
    // 在生产环境中，这里可以发送错误到监控服务
    if (process.env.NODE_ENV === 'production') {
      // 示例：发送到错误监控服务
      // errorReportingService.report({
      //   error,
      //   errorInfo,
      //   errorId: this.state.errorId,
      //   timestamp: this.state.timestamp,
      //   userAgent: navigator.userAgent,
      //   url: window.location.href,
      // });
    }
  };

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: undefined,
      errorInfo: undefined,
      errorId: '',
      timestamp: 0,
    });
  };

  render() {
    if (this.state.hasError) {
      const { fallback: Fallback, showDetails = true, level = 'component' } = this.props;
      const { error, errorInfo, errorId, timestamp } = this.state;

      if (Fallback && error) {
        return <Fallback error={error} retry={this.handleRetry} errorId={errorId} />;
      }

      return (
        <ErrorFallback
          error={error}
          errorInfo={errorInfo}
          errorId={errorId}
          timestamp={timestamp}
          onRetry={this.handleRetry}
          showDetails={showDetails}
          level={level}
        />
      );
    }

    return this.props.children;
  }
}

// 默认错误回退组件
interface ErrorFallbackProps {
  error?: Error;
  errorInfo?: ErrorInfo;
  errorId: string;
  timestamp: number;
  onRetry: () => void;
  showDetails: boolean;
  level: 'page' | 'section' | 'component';
}

const ErrorFallback: React.FC<ErrorFallbackProps> = ({
  error,
  errorInfo,
  errorId,
  timestamp,
  onRetry,
  showDetails,
  level,
}) => {
  const getLevelConfig = () => {
    switch (level) {
      case 'page':
        return {
          title: '页面加载失败',
          subTitle: '页面遇到了一些问题，请稍后重试',
          minHeight: '400px',
        };
      case 'section':
        return {
          title: '模块加载失败',
          subTitle: '该模块遇到了一些问题',
          minHeight: '200px',
        };
      case 'component':
        return {
          title: '组件错误',
          subTitle: '组件渲染失败',
          minHeight: '100px',
        };
    }
  };

  const config = getLevelConfig();

  const copyErrorInfo = () => {
    const errorText = `
错误ID: ${errorId}
时间: ${new Date(timestamp).toLocaleString()}
错误信息: ${error?.message || '未知错误'}
错误堆栈: ${error?.stack || '无堆栈信息'}
组件堆栈: ${errorInfo?.componentStack || '无组件堆栈'}
    `.trim();

    navigator.clipboard.writeText(errorText).then(() => {
      console.log('错误信息已复制到剪贴板');
    });
  };

  return (
    <div 
      className="flex items-center justify-center p-4"
      style={{ minHeight: config.minHeight }}
    >
      <Result
        status="error"
        title={config.title}
        subTitle={config.subTitle}
        extra={[
          <Button type="primary" icon={<ReloadOutlined />} onClick={onRetry} key="retry">
            重试
          </Button>,
          <Button key="home" onClick={() => window.location.href = '/dashboard'}>
            返回首页
          </Button>,
        ]}
      >
        {showDetails && error && (
          <div className="mt-4">
            <Collapse ghost>
              <Panel 
                header={
                  <span>
                    <BugOutlined className="mr-2" />
                    错误详情
                  </span>
                } 
                key="details"
              >
                <div className="space-y-4">
                  {/* 错误基本信息 */}
                  <div>
                    <div className="flex items-center space-x-2 mb-2">
                      <InfoCircleOutlined />
                      <Text strong>基本信息</Text>
                    </div>
                    <div className="bg-gray-50 p-3 rounded">
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div>错误ID: <Tag>{errorId}</Tag></div>
                        <div>时间: {new Date(timestamp).toLocaleString()}</div>
                        <div>类型: {error.name}</div>
                        <div>环境: {process.env.NODE_ENV}</div>
                      </div>
                    </div>
                  </div>

                  {/* 错误消息 */}
                  <div>
                    <div className="flex items-center space-x-2 mb-2">
                      <WarningOutlined />
                      <Text strong>错误消息</Text>
                    </div>
                    <Paragraph 
                      code 
                      copyable={{ text: error.message }}
                      className="bg-red-50 border border-red-200"
                    >
                      {error.message}
                    </Paragraph>
                  </div>

                  {/* 错误堆栈 */}
                  {error.stack && (
                    <div>
                      <Text strong>错误堆栈</Text>
                      <Paragraph 
                        code 
                        copyable={{ text: error.stack }}
                        className="bg-gray-50 max-h-40 overflow-auto text-xs"
                      >
                        {error.stack}
                      </Paragraph>
                    </div>
                  )}

                  {/* 组件堆栈 */}
                  {errorInfo?.componentStack && (
                    <div>
                      <Text strong>组件堆栈</Text>
                      <Paragraph 
                        code 
                        copyable={{ text: errorInfo.componentStack }}
                        className="bg-blue-50 max-h-40 overflow-auto text-xs"
                      >
                        {errorInfo.componentStack}
                      </Paragraph>
                    </div>
                  )}

                  {/* 操作按钮 */}
                  <div className="flex space-x-2">
                    <Button size="small" onClick={copyErrorInfo}>
                      复制错误信息
                    </Button>
                    <Button 
                      size="small" 
                      onClick={() => window.location.reload()}
                    >
                      刷新页面
                    </Button>
                  </div>
                </div>
              </Panel>
            </Collapse>
          </div>
        )}
      </Result>
    </div>
  );
};

// 简化的错误边界组件
export const SimpleErrorBoundary: React.FC<{
  children: React.ReactNode;
  fallback?: React.ReactNode;
}> = ({ children, fallback }) => {
  return (
    <EnhancedErrorBoundary
      level="component"
      showDetails={false}
      fallback={fallback ? () => <>{fallback}</> : undefined}
    >
      {children}
    </EnhancedErrorBoundary>
  );
};

// 页面级错误边界
export const PageErrorBoundary: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  return (
    <EnhancedErrorBoundary level="page" showDetails={true}>
      {children}
    </EnhancedErrorBoundary>
  );
};

// 模块级错误边界
export const SectionErrorBoundary: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  return (
    <EnhancedErrorBoundary level="section" showDetails={true}>
      {children}
    </EnhancedErrorBoundary>
  );
};
