import React from 'react';
import { Badge, Tooltip } from 'antd';
import { PluginStatus } from '@/lib/types/plugin';
import clsx from 'clsx';

interface StatusIndicatorProps {
  status: PluginStatus;
  size?: 'small' | 'default' | 'large';
  showText?: boolean;
  tooltip?: string;
  className?: string;
}

const statusConfig = {
  [PluginStatus.RUNNING]: {
    color: '#52c41a',
    text: '运行中',
    badgeStatus: 'success' as const,
    className: 'status-running',
  },
  [PluginStatus.ERROR]: {
    color: '#ff4d4f',
    text: '错误',
    badgeStatus: 'error' as const,
    className: 'status-error',
  },
  [PluginStatus.IDLE]: {
    color: '#faad14',
    text: '空闲',
    badgeStatus: 'warning' as const,
    className: 'status-idle',
  },
  [PluginStatus.STOPPED]: {
    color: '#d9d9d9',
    text: '已停止',
    badgeStatus: 'default' as const,
    className: 'status-stopped',
  },
};

export const StatusIndicator: React.FC<StatusIndicatorProps> = ({
  status,
  size = 'default',
  showText = false,
  tooltip,
  className,
}) => {
  const config = statusConfig[status];
  
  if (!config) {
    return null;
  }

  const indicator = (
    <div className={clsx('flex items-center space-x-2', className)}>
      <div
        className={clsx(
          'status-indicator',
          config.className,
          {
            'w-2 h-2': size === 'small',
            'w-3 h-3': size === 'default',
            'w-4 h-4': size === 'large',
          }
        )}
        style={{ backgroundColor: config.color }}
      />
      {showText && (
        <span
          className={clsx('text-sm', {
            'text-xs': size === 'small',
            'text-base': size === 'large',
          })}
        >
          {config.text}
        </span>
      )}
    </div>
  );

  if (tooltip) {
    return (
      <Tooltip title={tooltip}>
        {indicator}
      </Tooltip>
    );
  }

  return indicator;
};

// Badge 版本的状态指示器
export const StatusBadge: React.FC<StatusIndicatorProps> = ({
  status,
  showText = true,
  tooltip,
  className,
}) => {
  const config = statusConfig[status];
  
  if (!config) {
    return null;
  }

  const badge = (
    <Badge
      status={config.badgeStatus}
      text={showText ? config.text : undefined}
      className={className}
    />
  );

  if (tooltip) {
    return (
      <Tooltip title={tooltip}>
        {badge}
      </Tooltip>
    );
  }

  return badge;
};

// 状态统计组件
interface StatusStatsProps {
  stats: {
    total: number;
    running: number;
    error: number;
    idle: number;
    stopped: number;
  };
  className?: string;
}

export const StatusStats: React.FC<StatusStatsProps> = ({ stats, className }) => {
  return (
    <div className={clsx('flex items-center space-x-4', className)}>
      <div className="flex items-center space-x-1">
        <StatusIndicator status={PluginStatus.RUNNING} size="small" />
        <span className="text-sm text-gray-600">{stats.running}</span>
      </div>
      <div className="flex items-center space-x-1">
        <StatusIndicator status={PluginStatus.ERROR} size="small" />
        <span className="text-sm text-gray-600">{stats.error}</span>
      </div>
      <div className="flex items-center space-x-1">
        <StatusIndicator status={PluginStatus.IDLE} size="small" />
        <span className="text-sm text-gray-600">{stats.idle}</span>
      </div>
      <div className="flex items-center space-x-1">
        <StatusIndicator status={PluginStatus.STOPPED} size="small" />
        <span className="text-sm text-gray-600">{stats.stopped}</span>
      </div>
    </div>
  );
};
