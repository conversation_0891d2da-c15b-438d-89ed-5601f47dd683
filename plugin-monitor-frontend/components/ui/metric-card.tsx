import React from 'react';
import { Card, Statistic, Progress, Tooltip } from 'antd';
import {
  ArrowUpOutlined,
  ArrowDownOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons';
import clsx from 'clsx';

interface MetricCardProps {
  title: string;
  value: number | string;
  unit?: string;
  prefix?: React.ReactNode;
  suffix?: React.ReactNode;
  precision?: number;
  trend?: {
    value: number;
    isPositive?: boolean;
    label?: string;
  };
  progress?: {
    percent: number;
    status?: 'success' | 'exception' | 'active' | 'normal';
    strokeColor?: string;
  };
  description?: string;
  tooltip?: string;
  loading?: boolean;
  className?: string;
  size?: 'small' | 'default' | 'large';
  color?: 'blue' | 'green' | 'red' | 'orange' | 'purple';
}

const colorConfig = {
  blue: {
    bg: 'bg-blue-50',
    border: 'border-blue-200',
    text: 'text-blue-600',
    icon: 'text-blue-500',
  },
  green: {
    bg: 'bg-green-50',
    border: 'border-green-200',
    text: 'text-green-600',
    icon: 'text-green-500',
  },
  red: {
    bg: 'bg-red-50',
    border: 'border-red-200',
    text: 'text-red-600',
    icon: 'text-red-500',
  },
  orange: {
    bg: 'bg-orange-50',
    border: 'border-orange-200',
    text: 'text-orange-600',
    icon: 'text-orange-500',
  },
  purple: {
    bg: 'bg-purple-50',
    border: 'border-purple-200',
    text: 'text-purple-600',
    icon: 'text-purple-500',
  },
};

export const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  unit,
  prefix,
  suffix,
  precision,
  trend,
  progress,
  description,
  tooltip,
  loading = false,
  className,
  size = 'default',
  color = 'blue',
}) => {
  const colors = colorConfig[color];
  
  const cardSize = size === 'small' ? 'small' : 'default';
  
  const titleElement = (
    <div className="flex items-center space-x-1">
      <span className={clsx('font-medium', {
        'text-sm': size === 'small',
        'text-base': size === 'default',
        'text-lg': size === 'large',
      })}>
        {title}
      </span>
      {tooltip && (
        <Tooltip title={tooltip}>
          <InfoCircleOutlined className="text-gray-400 text-xs" />
        </Tooltip>
      )}
    </div>
  );

  return (
    <Card
      size={cardSize}
      loading={loading}
      className={clsx(
        'transition-all duration-200 hover:shadow-md',
        colors.bg,
        colors.border,
        className
      )}
    >
      <div className="space-y-3">
        {/* 标题 */}
        {titleElement}

        {/* 主要指标 */}
        <div className="flex items-end justify-between">
          <Statistic
            value={value}
            precision={precision}
            prefix={prefix}
            suffix={unit || suffix}
            valueStyle={{
              color: colors.text,
              fontSize: size === 'small' ? '20px' : size === 'large' ? '32px' : '24px',
              fontWeight: 600,
            }}
          />

          {/* 趋势指标 */}
          {trend && (
            <div className={clsx('flex items-center space-x-1', {
              'text-green-500': trend.isPositive,
              'text-red-500': !trend.isPositive,
            })}>
              {trend.isPositive ? (
                <ArrowUpOutlined className="text-xs" />
              ) : (
                <ArrowDownOutlined className="text-xs" />
              )}
              <span className="text-sm font-medium">
                {Math.abs(trend.value)}%
              </span>
              {trend.label && (
                <span className="text-xs text-gray-500">
                  {trend.label}
                </span>
              )}
            </div>
          )}
        </div>

        {/* 进度条 */}
        {progress && (
          <Progress
            percent={progress.percent}
            status={progress.status}
            strokeColor={progress.strokeColor}
            showInfo={false}
            size="small"
          />
        )}

        {/* 描述信息 */}
        {description && (
          <p className="text-xs text-gray-500 mt-2">
            {description}
          </p>
        )}
      </div>
    </Card>
  );
};

// 简化版指标卡片
export const SimpleMetricCard: React.FC<{
  title: string;
  value: number | string;
  unit?: string;
  color?: string;
  icon?: React.ReactNode;
}> = ({ title, value, unit, color = '#1890ff', icon }) => {
  return (
    <div className="bg-white rounded-lg p-4 shadow-sm border">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm text-gray-600 mb-1">{title}</p>
          <p className="text-2xl font-semibold" style={{ color }}>
            {value}
            {unit && <span className="text-sm ml-1">{unit}</span>}
          </p>
        </div>
        {icon && (
          <div className="text-2xl" style={{ color }}>
            {icon}
          </div>
        )}
      </div>
    </div>
  );
};

// 网格布局的指标卡片组
export const MetricCardGrid: React.FC<{
  metrics: Array<{
    key: string;
    title: string;
    value: number | string;
    unit?: string;
    color?: 'blue' | 'green' | 'red' | 'orange' | 'purple';
    trend?: {
      value: number;
      isPositive?: boolean;
    };
  }>;
  columns?: 2 | 3 | 4;
}> = ({ metrics, columns = 4 }) => {
  const gridCols = {
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
  };

  return (
    <div className={clsx('grid gap-4', gridCols[columns])}>
      {metrics.map((metric) => (
        <MetricCard
          key={metric.key}
          title={metric.title}
          value={metric.value}
          unit={metric.unit}
          color={metric.color}
          trend={metric.trend}
          size="small"
        />
      ))}
    </div>
  );
};
