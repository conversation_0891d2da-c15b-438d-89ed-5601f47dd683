import React from 'react';
import { Spin } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';
import clsx from 'clsx';

interface LoadingSpinnerProps {
  size?: 'small' | 'default' | 'large';
  tip?: string;
  spinning?: boolean;
  children?: React.ReactNode;
  className?: string;
  indicator?: React.ReactNode;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'default',
  tip,
  spinning = true,
  children,
  className,
  indicator,
}) => {
  const defaultIndicator = (
    <LoadingOutlined
      style={{
        fontSize: size === 'small' ? 14 : size === 'large' ? 24 : 18,
      }}
      spin
    />
  );

  if (children) {
    return (
      <Spin
        spinning={spinning}
        tip={tip}
        size={size}
        indicator={indicator || defaultIndicator}
        className={className}
      >
        {children}
      </Spin>
    );
  }

  return (
    <div className={clsx('flex items-center justify-center', className)}>
      <Spin
        spinning={spinning}
        tip={tip}
        size={size}
        indicator={indicator || defaultIndicator}
      />
    </div>
  );
};

// 页面级加载组件
export const PageLoading: React.FC<{ tip?: string }> = ({ tip = '加载中...' }) => {
  return (
    <div className="flex items-center justify-center min-h-[400px]">
      <LoadingSpinner size="large" tip={tip} />
    </div>
  );
};

// 内联加载组件
export const InlineLoading: React.FC<{ tip?: string }> = ({ tip }) => {
  return (
    <div className="flex items-center justify-center py-4">
      <LoadingSpinner size="small" tip={tip} />
    </div>
  );
};

// 按钮加载组件
export const ButtonLoading: React.FC = () => {
  return (
    <LoadingSpinner
      size="small"
      indicator={
        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
      }
    />
  );
};

// 卡片加载骨架
export const CardSkeleton: React.FC<{ rows?: number }> = ({ rows = 3 }) => {
  return (
    <div className="animate-pulse">
      <div className="h-4 bg-gray-200 rounded mb-3"></div>
      {Array.from({ length: rows }).map((_, index) => (
        <div key={index} className="space-y-2 mb-2">
          <div className="h-3 bg-gray-200 rounded"></div>
          <div className="h-3 bg-gray-200 rounded w-5/6"></div>
        </div>
      ))}
    </div>
  );
};

// 表格加载骨架
export const TableSkeleton: React.FC<{ rows?: number; columns?: number }> = ({ 
  rows = 5, 
  columns = 4 
}) => {
  return (
    <div className="animate-pulse">
      {/* 表头 */}
      <div className="flex space-x-4 mb-4">
        {Array.from({ length: columns }).map((_, index) => (
          <div key={index} className="flex-1 h-4 bg-gray-200 rounded"></div>
        ))}
      </div>
      
      {/* 表格行 */}
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div key={rowIndex} className="flex space-x-4 mb-3">
          {Array.from({ length: columns }).map((_, colIndex) => (
            <div key={colIndex} className="flex-1 h-3 bg-gray-100 rounded"></div>
          ))}
        </div>
      ))}
    </div>
  );
};
