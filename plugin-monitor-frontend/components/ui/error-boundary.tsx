'use client';

import React from 'react';
import { Result, Button } from 'antd';
import { ExceptionOutlined, ReloadOutlined } from '@ant-design/icons';

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error: Error; retry: () => void }>;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    this.setState({
      error,
      errorInfo,
    });

    // 调用错误处理回调
    this.props.onError?.(error, errorInfo);

    // 在开发环境下打印错误信息
    if (process.env.NODE_ENV === 'development') {
      console.error('ErrorBoundary caught an error:', error, errorInfo);
    }
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      const { fallback: Fallback } = this.props;
      
      if (Fallback && this.state.error) {
        return <Fallback error={this.state.error} retry={this.handleRetry} />;
      }

      return (
        <DefaultErrorFallback 
          error={this.state.error} 
          retry={this.handleRetry}
        />
      );
    }

    return this.props.children;
  }
}

// 默认错误回退组件
interface DefaultErrorFallbackProps {
  error?: Error;
  retry: () => void;
}

const DefaultErrorFallback: React.FC<DefaultErrorFallbackProps> = ({ error, retry }) => {
  return (
    <div className="min-h-[400px] flex items-center justify-center">
      <Result
        status="error"
        title="出现了一些问题"
        subTitle={
          process.env.NODE_ENV === 'development' 
            ? error?.message || '未知错误'
            : '页面加载失败，请稍后重试'
        }
        extra={[
          <Button type="primary" icon={<ReloadOutlined />} onClick={retry} key="retry">
            重试
          </Button>,
          <Button key="home" onClick={() => window.location.href = '/dashboard'}>
            返回首页
          </Button>,
        ]}
      />
    </div>
  );
};

// 简化的错误回退组件
export const SimpleErrorFallback: React.FC<DefaultErrorFallbackProps> = ({ error, retry }) => {
  return (
    <div className="text-center py-8">
      <ExceptionOutlined className="text-4xl text-red-500 mb-4" />
      <h3 className="text-lg font-medium text-gray-800 mb-2">加载失败</h3>
      <p className="text-gray-600 mb-4">
        {process.env.NODE_ENV === 'development' 
          ? error?.message || '未知错误'
          : '请稍后重试'
        }
      </p>
      <Button type="primary" size="small" onClick={retry}>
        重试
      </Button>
    </div>
  );
};

// 卡片级错误边界
export const CardErrorBoundary: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <ErrorBoundary fallback={SimpleErrorFallback}>
      {children}
    </ErrorBoundary>
  );
};

// Hook 版本的错误边界
export const useErrorHandler = () => {
  const [error, setError] = React.useState<Error | null>(null);

  const resetError = React.useCallback(() => {
    setError(null);
  }, []);

  const handleError = React.useCallback((error: Error) => {
    setError(error);
  }, []);

  React.useEffect(() => {
    if (error) {
      throw error;
    }
  }, [error]);

  return { handleError, resetError };
};
