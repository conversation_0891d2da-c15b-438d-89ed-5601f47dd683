'use client';

import React, { useRef, useEffect, useState } from 'react';
import * as echarts from 'echarts';
import { Spin, Empty, Button } from 'antd';
import { ReloadOutlined, FullscreenOutlined } from '@ant-design/icons';
import clsx from 'clsx';

export interface BaseChartProps {
  option: echarts.EChartsOption;
  loading?: boolean;
  error?: string;
  height?: number | string;
  width?: number | string;
  className?: string;
  theme?: 'light' | 'dark';
  onChartReady?: (chart: echarts.ECharts) => void;
  onRefresh?: () => void;
  showToolbar?: boolean;
  autoResize?: boolean;
}

export const BaseChart: React.FC<BaseChartProps> = ({
  option,
  loading = false,
  error,
  height = 400,
  width = '100%',
  className,
  theme = 'light',
  onChartReady,
  onRefresh,
  showToolbar = false,
  autoResize = true,
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // 初始化图表
  useEffect(() => {
    if (!chartRef.current) return;

    // 销毁现有实例
    if (chartInstance.current) {
      chartInstance.current.dispose();
    }

    // 创建新实例
    chartInstance.current = echarts.init(chartRef.current, theme);
    
    // 通知父组件图表已准备就绪
    onChartReady?.(chartInstance.current);

    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
        chartInstance.current = null;
      }
    };
  }, [theme, onChartReady]);

  // 更新图表配置
  useEffect(() => {
    if (chartInstance.current && option) {
      chartInstance.current.setOption(option, true);
    }
  }, [option]);

  // 自动调整大小
  useEffect(() => {
    if (!autoResize || !chartInstance.current) return;

    const handleResize = () => {
      chartInstance.current?.resize();
    };

    window.addEventListener('resize', handleResize);
    
    // 使用 ResizeObserver 监听容器大小变化
    let resizeObserver: ResizeObserver | null = null;
    if (chartRef.current && 'ResizeObserver' in window) {
      resizeObserver = new ResizeObserver(handleResize);
      resizeObserver.observe(chartRef.current);
    }

    return () => {
      window.removeEventListener('resize', handleResize);
      resizeObserver?.disconnect();
    };
  }, [autoResize]);

  // 全屏切换
  const toggleFullscreen = () => {
    if (!chartRef.current) return;

    if (!isFullscreen) {
      if (chartRef.current.requestFullscreen) {
        chartRef.current.requestFullscreen();
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      }
    }
  };

  // 监听全屏状态变化
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
      // 全屏状态变化时调整图表大小
      setTimeout(() => {
        chartInstance.current?.resize();
      }, 100);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);

  // 错误状态
  if (error) {
    return (
      <div 
        className={clsx('flex items-center justify-center bg-gray-50 rounded', className)}
        style={{ height, width }}
      >
        <div className="text-center">
          <div className="text-red-500 mb-2">图表加载失败</div>
          <div className="text-gray-500 text-sm mb-4">{error}</div>
          {onRefresh && (
            <Button 
              icon={<ReloadOutlined />} 
              onClick={onRefresh}
              size="small"
            >
              重试
            </Button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={clsx('relative', className)}>
      {/* 工具栏 */}
      {showToolbar && (
        <div className="absolute top-2 right-2 z-10 flex space-x-1">
          {onRefresh && (
            <Button
              type="text"
              size="small"
              icon={<ReloadOutlined />}
              onClick={onRefresh}
              className="bg-white shadow-sm"
            />
          )}
          <Button
            type="text"
            size="small"
            icon={<FullscreenOutlined />}
            onClick={toggleFullscreen}
            className="bg-white shadow-sm"
          />
        </div>
      )}

      {/* 加载状态 */}
      {loading && (
        <div 
          className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 z-20"
        >
          <Spin size="large" />
        </div>
      )}

      {/* 图表容器 */}
      <div
        ref={chartRef}
        className={clsx('chart-container', {
          'fullscreen-chart': isFullscreen,
        })}
        style={{ 
          height, 
          width,
          minHeight: typeof height === 'number' ? `${height}px` : height,
        }}
      />

      {/* 全屏样式 */}
      <style jsx>{`
        .fullscreen-chart {
          width: 100vw !important;
          height: 100vh !important;
        }
      `}</style>
    </div>
  );
};

// 图表容器组件
export const ChartContainer: React.FC<{
  title?: string;
  children: React.ReactNode;
  className?: string;
  extra?: React.ReactNode;
}> = ({ title, children, className, extra }) => {
  return (
    <div className={clsx('bg-white rounded-lg shadow-sm border', className)}>
      {(title || extra) && (
        <div className="flex items-center justify-between p-4 border-b">
          {title && <h3 className="text-lg font-medium">{title}</h3>}
          {extra}
        </div>
      )}
      <div className="p-4">
        {children}
      </div>
    </div>
  );
};

// 空状态组件
export const ChartEmpty: React.FC<{
  description?: string;
  onRefresh?: () => void;
}> = ({ description = '暂无数据', onRefresh }) => {
  return (
    <div className="flex items-center justify-center h-64">
      <Empty
        description={description}
        image={Empty.PRESENTED_IMAGE_SIMPLE}
      >
        {onRefresh && (
          <Button type="primary" onClick={onRefresh}>
            刷新数据
          </Button>
        )}
      </Empty>
    </div>
  );
};
