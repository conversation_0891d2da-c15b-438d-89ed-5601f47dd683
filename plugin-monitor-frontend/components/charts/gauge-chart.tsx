'use client';

import React, { useMemo } from 'react';
import { BaseChart, BaseChartProps } from './base-chart';
import * as echarts from 'echarts';

export interface GaugeChartProps extends Omit<BaseChartProps, 'option'> {
  value: number;
  min?: number;
  max?: number;
  unit?: string;
  title?: string;
  thresholds?: Array<{
    value: number;
    color: string;
    label?: string;
  }>;
  showProgress?: boolean;
  size?: 'small' | 'default' | 'large';
}

export const GaugeChart: React.FC<GaugeChartProps> = ({
  value,
  min = 0,
  max = 100,
  unit = '%',
  title,
  thresholds = [
    { value: 60, color: '#52c41a', label: '正常' },
    { value: 80, color: '#faad14', label: '警告' },
    { value: 100, color: '#f5222d', label: '危险' },
  ],
  showProgress = true,
  size = 'default',
  ...baseProps
}) => {
  const option = useMemo((): echarts.EChartsOption => {
    // 根据值确定颜色
    const getColor = (val: number) => {
      for (let i = 0; i < thresholds.length; i++) {
        if (val <= thresholds[i].value) {
          return thresholds[i].color;
        }
      }
      return thresholds[thresholds.length - 1].color;
    };

    const currentColor = getColor(value);
    
    // 根据尺寸调整大小
    const sizeConfig = {
      small: { radius: '60%', fontSize: 12, titleFontSize: 14 },
      default: { radius: '70%', fontSize: 16, titleFontSize: 18 },
      large: { radius: '80%', fontSize: 20, titleFontSize: 22 },
    };
    
    const config = sizeConfig[size];

    return {
      series: [
        {
          type: 'gauge',
          radius: config.radius,
          center: ['50%', '55%'],
          startAngle: 200,
          endAngle: -40,
          min,
          max,
          splitNumber: 5,
          itemStyle: {
            color: currentColor,
            shadowColor: 'rgba(0,138,255,0.45)',
            shadowBlur: 10,
            shadowOffsetX: 2,
            shadowOffsetY: 2,
          },
          progress: showProgress ? {
            show: true,
            roundCap: true,
            width: 8,
          } : undefined,
          pointer: {
            icon: 'path://M2090.36389,615.30999 L2090.36389,615.30999 C2091.48372,615.30999 2092.40383,616.194028 2092.44859,617.312956 L2096.90698,728.755929 C2097.05155,732.369577 2094.2393,735.416212 2090.62566,735.56078 C2090.53845,735.564269 2090.45117,735.566014 2090.36389,735.566014 L2090.36389,735.566014 C2086.74736,735.566014 2083.81557,732.63423 2083.81557,729.017692 C2083.81557,728.930412 2083.81732,728.84314 2083.82081,728.755929 L2088.2792,617.312956 C2088.32396,616.194028 2089.24407,615.30999 2090.36389,615.30999 Z',
            length: '75%',
            width: 8,
            offsetCenter: [0, '5%'],
            itemStyle: {
              color: currentColor,
            },
          },
          axisLine: {
            roundCap: true,
            lineStyle: {
              width: 8,
              color: [
                [0.3, '#52c41a'],
                [0.7, '#faad14'],
                [1, '#f5222d'],
              ],
            },
          },
          axisTick: {
            distance: -30,
            splitNumber: 5,
            lineStyle: {
              width: 2,
              color: '#999',
            },
          },
          splitLine: {
            distance: -30,
            length: 14,
            lineStyle: {
              width: 3,
              color: '#999',
            },
          },
          axisLabel: {
            distance: -20,
            color: '#999',
            fontSize: config.fontSize - 4,
          },
          anchor: {
            show: false,
          },
          title: {
            show: !!title,
            offsetCenter: [0, '20%'],
            fontSize: config.titleFontSize,
            color: '#464646',
          },
          detail: {
            valueAnimation: true,
            width: '60%',
            lineHeight: 40,
            borderRadius: 8,
            offsetCenter: [0, '35%'],
            fontSize: config.fontSize + 4,
            fontWeight: 'bolder',
            formatter: `{value}${unit}`,
            color: currentColor,
          },
          data: [
            {
              value,
              name: title || '',
            },
          ],
        },
      ],
    };
  }, [value, min, max, unit, title, thresholds, showProgress, size]);

  return <BaseChart option={option} {...baseProps} />;
};

// 多仪表盘组件
export const MultiGaugeChart: React.FC<{
  gauges: Array<{
    value: number;
    title: string;
    unit?: string;
    min?: number;
    max?: number;
    color?: string;
  }>;
  height?: number;
  className?: string;
}> = ({ gauges, height = 300, className }) => {
  const option = useMemo((): echarts.EChartsOption => {
    const cols = Math.ceil(Math.sqrt(gauges.length));
    const rows = Math.ceil(gauges.length / cols);
    
    return {
      series: gauges.map((gauge, index) => {
        const row = Math.floor(index / cols);
        const col = index % cols;
        const centerX = (col + 0.5) / cols * 100;
        const centerY = (row + 0.5) / rows * 100;
        const radius = Math.min(80 / cols, 80 / rows);

        return {
          type: 'gauge',
          radius: `${radius}%`,
          center: [`${centerX}%`, `${centerY}%`],
          min: gauge.min || 0,
          max: gauge.max || 100,
          splitNumber: 3,
          itemStyle: {
            color: gauge.color || '#1890ff',
          },
          progress: {
            show: true,
            width: 4,
          },
          pointer: {
            show: false,
          },
          axisLine: {
            lineStyle: {
              width: 4,
            },
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            show: false,
          },
          axisLabel: {
            show: false,
          },
          title: {
            fontSize: 12,
            offsetCenter: [0, '80%'],
          },
          detail: {
            fontSize: 14,
            offsetCenter: [0, 0],
            formatter: `{value}${gauge.unit || '%'}`,
          },
          data: [
            {
              value: gauge.value,
              name: gauge.title,
            },
          ],
        };
      }),
    };
  }, [gauges]);

  return (
    <BaseChart 
      option={option} 
      height={height} 
      className={className}
    />
  );
};

// 简化的进度环组件
export const ProgressRing: React.FC<{
  value: number;
  max?: number;
  size?: number;
  strokeWidth?: number;
  color?: string;
  backgroundColor?: string;
  showText?: boolean;
  unit?: string;
}> = ({
  value,
  max = 100,
  size = 120,
  strokeWidth = 8,
  color = '#1890ff',
  backgroundColor = '#f0f0f0',
  showText = true,
  unit = '%',
}) => {
  const percentage = (value / max) * 100;
  const radius = (size - strokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;

  return (
    <div className="relative inline-flex items-center justify-center">
      <svg width={size} height={size} className="transform -rotate-90">
        {/* 背景圆环 */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={backgroundColor}
          strokeWidth={strokeWidth}
          fill="none"
        />
        {/* 进度圆环 */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={color}
          strokeWidth={strokeWidth}
          fill="none"
          strokeLinecap="round"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          className="transition-all duration-300 ease-in-out"
        />
      </svg>
      {showText && (
        <div className="absolute inset-0 flex items-center justify-center">
          <span className="text-lg font-semibold" style={{ color }}>
            {value}{unit}
          </span>
        </div>
      )}
    </div>
  );
};
