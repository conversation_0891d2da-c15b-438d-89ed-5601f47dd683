'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Card, Row, Col, Statistic, Alert, Button, Space } from 'antd';
import { 
  ReloadOutlined, 
  PauseCircleOutlined, 
  PlayCircleOutlined,
  SettingOutlined 
} from '@ant-design/icons';
import { <PERSON><PERSON>hart, RealTimeLineChart } from './line-chart';
import { GaugeChart, MultiGaugeChart } from './gauge-chart';
import { MetricCard } from '@/components/ui/metric-card';
import { useWebSocket } from '@/lib/services/websocket';
import { usePluginStore } from '@/lib/stores/plugin-store';

interface RealTimeMetric {
  timestamp: number;
  cpu_usage: number;
  memory_usage: number;
  network_io: number;
  error_rate: number;
}

export const RealTimeDashboard: React.FC = () => {
  const [isRunning, setIsRunning] = useState(true);
  const [metrics, setMetrics] = useState<RealTimeMetric[]>([]);
  const [currentMetrics, setCurrentMetrics] = useState<RealTimeMetric>({
    timestamp: Date.now(),
    cpu_usage: 0,
    memory_usage: 0,
    network_io: 0,
    error_rate: 0,
  });

  const { stats } = usePluginStore();
  const { connected, subscribe } = useWebSocket();

  // 生成模拟数据
  const generateMockData = useCallback((): RealTimeMetric => {
    return {
      timestamp: Date.now(),
      cpu_usage: Math.random() * 100,
      memory_usage: Math.random() * 100,
      network_io: Math.random() * 1000,
      error_rate: Math.random() * 10,
    };
  }, []);

  // 添加新的指标数据
  const addMetric = useCallback((newMetric: RealTimeMetric) => {
    setMetrics(prev => {
      const updated = [...prev, newMetric];
      // 保持最新的100个数据点
      return updated.slice(-100);
    });
    setCurrentMetrics(newMetric);
  }, []);

  // 定时生成数据
  useEffect(() => {
    if (!isRunning) return;

    const interval = setInterval(() => {
      const newMetric = generateMockData();
      addMetric(newMetric);
    }, 2000);

    return () => clearInterval(interval);
  }, [isRunning, generateMockData, addMetric]);

  // WebSocket 数据订阅
  useEffect(() => {
    if (connected) {
      const unsubscribe = subscribe('system_metrics', (data: RealTimeMetric) => {
        addMetric(data);
      });

      return unsubscribe;
    }
  }, [connected, subscribe, addMetric]);

  // 准备图表数据
  const chartData = {
    cpu: {
      name: 'CPU使用率',
      data: metrics.map(m => [m.timestamp, m.cpu_usage]),
      color: '#1890ff',
    },
    memory: {
      name: '内存使用率',
      data: metrics.map(m => [m.timestamp, m.memory_usage]),
      color: '#52c41a',
    },
    network: {
      name: '网络IO',
      data: metrics.map(m => [m.timestamp, m.network_io]),
      color: '#faad14',
    },
    errorRate: {
      name: '错误率',
      data: metrics.map(m => [m.timestamp, m.error_rate]),
      color: '#f5222d',
    },
  };

  const gaugeData = [
    {
      value: currentMetrics.cpu_usage,
      title: 'CPU',
      unit: '%',
      color: '#1890ff',
    },
    {
      value: currentMetrics.memory_usage,
      title: '内存',
      unit: '%',
      color: '#52c41a',
    },
    {
      value: currentMetrics.error_rate,
      title: '错误率',
      unit: '%',
      max: 10,
      color: '#f5222d',
    },
  ];

  const toggleMonitoring = () => {
    setIsRunning(!isRunning);
  };

  const clearData = () => {
    setMetrics([]);
    setCurrentMetrics({
      timestamp: Date.now(),
      cpu_usage: 0,
      memory_usage: 0,
      network_io: 0,
      error_rate: 0,
    });
  };

  return (
    <div className="space-y-6">
      {/* 控制面板 */}
      <Card size="small">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <div className={`w-3 h-3 rounded-full ${connected ? 'bg-green-500' : 'bg-red-500'}`} />
              <span className="text-sm">
                {connected ? '实时连接已建立' : '连接已断开'}
              </span>
            </div>
            <div className="text-sm text-gray-500">
              数据点: {metrics.length}
            </div>
          </div>
          
          <Space>
            <Button
              icon={isRunning ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
              onClick={toggleMonitoring}
              type={isRunning ? 'default' : 'primary'}
            >
              {isRunning ? '暂停' : '开始'}
            </Button>
            <Button icon={<ReloadOutlined />} onClick={clearData}>
              清空数据
            </Button>
            <Button icon={<SettingOutlined />}>
              设置
            </Button>
          </Space>
        </div>
      </Card>

      {/* 关键指标卡片 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} lg={6}>
          <MetricCard
            title="插件总数"
            value={stats.total}
            color="blue"
            trend={{
              value: 5.2,
              isPositive: true,
              label: '较昨日',
            }}
          />
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <MetricCard
            title="运行中"
            value={stats.running}
            color="green"
            progress={{
              percent: (stats.running / stats.total) * 100,
              status: 'success',
            }}
          />
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <MetricCard
            title="CPU使用率"
            value={currentMetrics.cpu_usage.toFixed(1)}
            unit="%"
            color="blue"
            trend={{
              value: 2.1,
              isPositive: false,
            }}
          />
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <MetricCard
            title="内存使用率"
            value={currentMetrics.memory_usage.toFixed(1)}
            unit="%"
            color="orange"
            progress={{
              percent: currentMetrics.memory_usage,
              status: currentMetrics.memory_usage > 80 ? 'exception' : 'active',
            }}
          />
        </Col>
      </Row>

      {/* 实时图表 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={16}>
          <Card title="系统性能趋势" size="small">
            <RealTimeLineChart
              data={[chartData.cpu, chartData.memory]}
              height={300}
              showDataZoom={true}
              yAxisMax={100}
              yAxisLabel="使用率 (%)"
              maxDataPoints={50}
            />
          </Card>
        </Col>
        <Col xs={24} lg={8}>
          <Card title="实时指标" size="small">
            <MultiGaugeChart
              gauges={gaugeData}
              height={300}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card title="网络IO" size="small">
            <RealTimeLineChart
              data={[chartData.network]}
              height={250}
              yAxisLabel="KB/s"
              colors={['#faad14']}
            />
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="错误率监控" size="small">
            <RealTimeLineChart
              data={[chartData.errorRate]}
              height={250}
              yAxisMax={10}
              yAxisLabel="错误率 (%)"
              colors={['#f5222d']}
            />
            {currentMetrics.error_rate > 5 && (
              <Alert
                message="错误率过高"
                description="当前错误率超过5%，请检查系统状态"
                type="warning"
                showIcon
                className="mt-2"
              />
            )}
          </Card>
        </Col>
      </Row>
    </div>
  );
};
