'use client';

import React, { useMemo } from 'react';
import { BaseChart, BaseChartProps } from './base-chart';
import * as echarts from 'echarts';

export interface LineChartData {
  name: string;
  data: Array<[number | string, number]>; // [时间, 值]
  color?: string;
  smooth?: boolean;
  area?: boolean;
}

export interface LineChartProps extends Omit<BaseChartProps, 'option'> {
  data: LineChartData[];
  xAxisType?: 'time' | 'category' | 'value';
  yAxisType?: 'value' | 'log';
  showGrid?: boolean;
  showLegend?: boolean;
  showTooltip?: boolean;
  showDataZoom?: boolean;
  yAxisMin?: number;
  yAxisMax?: number;
  xAxisLabel?: string;
  yAxisLabel?: string;
  colors?: string[];
}

export const LineChart: React.FC<LineChartProps> = ({
  data,
  xAxisType = 'time',
  yAxisType = 'value',
  showGrid = true,
  showLegend = true,
  showTooltip = true,
  showDataZoom = false,
  yAxisMin,
  yAxisMax,
  xAxisLabel,
  yAxisLabel,
  colors = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1'],
  ...baseProps
}) => {
  const option = useMemo((): echarts.EChartsOption => {
    return {
      color: colors,
      tooltip: showTooltip ? {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985'
          }
        },
        formatter: (params: any) => {
          if (!Array.isArray(params)) return '';
          
          let result = '';
          if (xAxisType === 'time') {
            result = `${new Date(params[0].axisValue).toLocaleString()}<br/>`;
          } else {
            result = `${params[0].axisValue}<br/>`;
          }
          
          params.forEach((param: any) => {
            result += `${param.marker} ${param.seriesName}: ${param.value[1]}<br/>`;
          });
          
          return result;
        }
      } : undefined,
      legend: showLegend ? {
        data: data.map(item => item.name),
        top: 10,
      } : undefined,
      grid: showGrid ? {
        left: '3%',
        right: '4%',
        bottom: showDataZoom ? '15%' : '3%',
        top: showLegend ? '15%' : '3%',
        containLabel: true
      } : undefined,
      xAxis: {
        type: xAxisType,
        boundaryGap: false,
        name: xAxisLabel,
        nameLocation: 'middle',
        nameGap: 30,
        axisLine: {
          lineStyle: {
            color: '#d9d9d9'
          }
        },
        splitLine: {
          show: showGrid,
          lineStyle: {
            color: '#f0f0f0'
          }
        }
      },
      yAxis: {
        type: yAxisType,
        name: yAxisLabel,
        nameLocation: 'middle',
        nameGap: 50,
        min: yAxisMin,
        max: yAxisMax,
        axisLine: {
          lineStyle: {
            color: '#d9d9d9'
          }
        },
        splitLine: {
          show: showGrid,
          lineStyle: {
            color: '#f0f0f0'
          }
        }
      },
      dataZoom: showDataZoom ? [
        {
          type: 'inside',
          start: 0,
          end: 100
        },
        {
          start: 0,
          end: 100,
          height: 30,
        }
      ] : undefined,
      series: data.map((item, index) => ({
        name: item.name,
        type: 'line',
        data: item.data,
        smooth: item.smooth ?? true,
        symbol: 'circle',
        symbolSize: 4,
        lineStyle: {
          width: 2,
          color: item.color || colors[index % colors.length]
        },
        itemStyle: {
          color: item.color || colors[index % colors.length]
        },
        areaStyle: item.area ? {
          opacity: 0.3,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: item.color || colors[index % colors.length]
            },
            {
              offset: 1,
              color: 'rgba(255, 255, 255, 0)'
            }
          ])
        } : undefined,
        emphasis: {
          focus: 'series'
        }
      }))
    };
  }, [data, xAxisType, yAxisType, showGrid, showLegend, showTooltip, showDataZoom, yAxisMin, yAxisMax, xAxisLabel, yAxisLabel, colors]);

  return <BaseChart option={option} {...baseProps} />;
};

// 实时折线图组件
export const RealTimeLineChart: React.FC<LineChartProps & {
  maxDataPoints?: number;
  updateInterval?: number;
}> = ({ 
  data, 
  maxDataPoints = 100,
  updateInterval = 1000,
  ...props 
}) => {
  // 限制数据点数量
  const limitedData = useMemo(() => {
    return data.map(series => ({
      ...series,
      data: series.data.slice(-maxDataPoints)
    }));
  }, [data, maxDataPoints]);

  return (
    <LineChart
      data={limitedData}
      xAxisType="time"
      showDataZoom={true}
      showTooltip={true}
      {...props}
    />
  );
};

// 简化的趋势图组件
export const TrendChart: React.FC<{
  data: number[];
  timestamps: number[];
  color?: string;
  height?: number;
  smooth?: boolean;
}> = ({ 
  data, 
  timestamps, 
  color = '#1890ff', 
  height = 200,
  smooth = true 
}) => {
  const chartData: LineChartData[] = useMemo(() => [{
    name: '趋势',
    data: timestamps.map((time, index) => [time, data[index]]),
    color,
    smooth,
    area: true
  }], [data, timestamps, color, smooth]);

  return (
    <LineChart
      data={chartData}
      height={height}
      showGrid={false}
      showLegend={false}
      showTooltip={true}
      xAxisType="time"
    />
  );
};
