'use client';

import React, { useState } from 'react';
import { Table, Tag, Button, Space, Input, Select, Tooltip, Modal } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import {
  SearchOutlined,
  FilterOutlined,
  ReloadOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  SettingOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import { PluginState, PluginConfig, PluginStatus, PluginType } from '@/lib/types/plugin';
import { StatusIndicator } from '@/components/ui/status-indicator';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';

dayjs.extend(relativeTime);

const { Search } = Input;
const { Option } = Select;

interface PluginListProps {
  plugins: PluginState[];
  configs: Record<string, PluginConfig>;
  loading?: boolean;
  onAction?: (action: string, pluginId: string) => void;
  onRefresh?: () => void;
}

interface PluginTableData extends PluginState {
  config?: PluginConfig;
}

export const PluginList: React.FC<PluginListProps> = ({
  plugins,
  configs,
  loading = false,
  onAction,
  onRefresh,
}) => {
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  // 合并插件状态和配置数据
  const tableData: PluginTableData[] = plugins.map(plugin => ({
    ...plugin,
    config: configs[plugin.plugin_id],
  }));

  // 过滤数据
  const filteredData = tableData.filter(plugin => {
    // 搜索过滤
    if (searchText) {
      const searchLower = searchText.toLowerCase();
      const matchesSearch = 
        plugin.plugin_id.toLowerCase().includes(searchLower) ||
        (plugin.config?.name?.toLowerCase().includes(searchLower)) ||
        plugin.plugin_type.toLowerCase().includes(searchLower);
      
      if (!matchesSearch) return false;
    }

    // 状态过滤
    if (statusFilter !== 'all' && plugin.status !== statusFilter) {
      return false;
    }

    // 类型过滤
    if (typeFilter !== 'all' && plugin.plugin_type !== typeFilter) {
      return false;
    }

    return true;
  });

  const handleAction = (action: string, pluginId: string) => {
    onAction?.(action, pluginId);
  };

  const handleBatchAction = (action: string) => {
    if (selectedRowKeys.length === 0) {
      Modal.warning({
        title: '请选择插件',
        content: '请先选择要操作的插件',
      });
      return;
    }

    Modal.confirm({
      title: `确认${action}操作`,
      content: `确定要对选中的 ${selectedRowKeys.length} 个插件执行${action}操作吗？`,
      onOk: () => {
        selectedRowKeys.forEach(key => {
          handleAction(action, key as string);
        });
        setSelectedRowKeys([]);
      },
    });
  };

  const getTypeText = (type: string) => {
    switch (type.toLowerCase()) {
      case 'inbound':
        return '输入插件';
      case 'outbound':
        return '输出插件';
      case 'rule':
        return '规则插件';
      case 'flow':
        return '流程插件';
      default:
        return type;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'inbound':
        return 'blue';
      case 'outbound':
        return 'green';
      case 'rule':
        return 'orange';
      case 'flow':
        return 'purple';
      default:
        return 'default';
    }
  };

  const columns: ColumnsType<PluginTableData> = [
    {
      title: '插件名称',
      dataIndex: 'plugin_id',
      key: 'name',
      width: 200,
      render: (_, record) => (
        <div>
          <div className="font-medium">
            {record.config?.name || record.plugin_id}
          </div>
          <div className="text-xs text-gray-500">
            ID: {record.plugin_id}
          </div>
        </div>
      ),
    },
    {
      title: '类型',
      dataIndex: 'plugin_type',
      key: 'type',
      width: 120,
      render: (type: string) => (
        <Tag color={getTypeColor(type)}>
          {getTypeText(type)}
        </Tag>
      ),
    },
    {
      title: '协议',
      dataIndex: 'config',
      key: 'protocol',
      width: 100,
      render: (config: PluginConfig) => (
        config?.protocol ? (
          <Tag>{config.protocol.toUpperCase()}</Tag>
        ) : (
          <span className="text-gray-400">-</span>
        )
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: PluginStatus) => (
        <StatusIndicator status={status} showText />
      ),
    },
    {
      title: '性能指标',
      key: 'metrics',
      width: 150,
      render: (_, record) => (
        <div className="text-xs space-y-1">
          {record.details?.memory_usage && (
            <div>内存: {record.details.memory_usage}</div>
          )}
          {record.details?.cpu_usage && (
            <div>CPU: {record.details.cpu_usage}</div>
          )}
        </div>
      ),
    },
    {
      title: '最后活动',
      dataIndex: 'timestamp',
      key: 'lastActivity',
      width: 120,
      render: (timestamp: number) => (
        <Tooltip title={dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss')}>
          <span className="text-xs">
            {dayjs(timestamp).fromNow()}
          </span>
        </Tooltip>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleAction('details', record.plugin_id)}
            />
          </Tooltip>
          
          {record.status === PluginStatus.RUNNING ? (
            <Tooltip title="停止">
              <Button
                type="text"
                size="small"
                icon={<PauseCircleOutlined />}
                onClick={() => handleAction('stop', record.plugin_id)}
              />
            </Tooltip>
          ) : (
            <Tooltip title="启动">
              <Button
                type="text"
                size="small"
                icon={<PlayCircleOutlined />}
                onClick={() => handleAction('start', record.plugin_id)}
              />
            </Tooltip>
          )}
          
          <Tooltip title="配置">
            <Button
              type="text"
              size="small"
              icon={<SettingOutlined />}
              onClick={() => handleAction('configure', record.plugin_id)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
    getCheckboxProps: (record: PluginTableData) => ({
      disabled: false,
      name: record.plugin_id,
    }),
  };

  return (
    <div className="space-y-4">
      {/* 工具栏 */}
      <div className="flex items-center justify-between">
        <Space size="middle">
          <Search
            placeholder="搜索插件名称或ID"
            allowClear
            style={{ width: 300 }}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            prefix={<SearchOutlined />}
          />
          
          <Select
            placeholder="状态筛选"
            style={{ width: 120 }}
            value={statusFilter}
            onChange={setStatusFilter}
          >
            <Option value="all">全部状态</Option>
            <Option value={PluginStatus.RUNNING}>运行中</Option>
            <Option value={PluginStatus.ERROR}>错误</Option>
            <Option value={PluginStatus.IDLE}>空闲</Option>
            <Option value={PluginStatus.STOPPED}>已停止</Option>
          </Select>

          <Select
            placeholder="类型筛选"
            style={{ width: 120 }}
            value={typeFilter}
            onChange={setTypeFilter}
          >
            <Option value="all">全部类型</Option>
            <Option value={PluginType.INBOUND}>输入插件</Option>
            <Option value={PluginType.OUTBOUND}>输出插件</Option>
            <Option value={PluginType.RULE}>规则插件</Option>
            <Option value={PluginType.FLOW}>流程插件</Option>
          </Select>
        </Space>

        <Space>
          {selectedRowKeys.length > 0 && (
            <>
              <Button
                size="small"
                onClick={() => handleBatchAction('start')}
              >
                批量启动
              </Button>
              <Button
                size="small"
                onClick={() => handleBatchAction('stop')}
              >
                批量停止
              </Button>
            </>
          )}
          
          <Button
            icon={<ReloadOutlined />}
            onClick={onRefresh}
            loading={loading}
          >
            刷新
          </Button>
        </Space>
      </div>

      {/* 表格 */}
      <Table
        columns={columns}
        dataSource={filteredData}
        rowKey="plugin_id"
        loading={loading}
        rowSelection={rowSelection}
        pagination={{
          total: filteredData.length,
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
        }}
        scroll={{ x: 1000 }}
        size="small"
      />
    </div>
  );
};
