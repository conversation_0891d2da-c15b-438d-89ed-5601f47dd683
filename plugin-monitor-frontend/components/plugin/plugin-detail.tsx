'use client';

import React, { useState, useEffect } from 'react';
import { 
  Drawer, 
  Tabs, 
  Card, 
  Descriptions, 
  Timeline, 
  Table, 
  Tag, 
  Button, 
  Space,
  Alert,
  Progress,
  Statistic,
  Row,
  Col
} from 'antd';
import {
  CloseOutlined,
  ReloadOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import { PluginState, PluginConfig, PluginData, PluginLog } from '@/lib/types/plugin';
import { StatusIndicator } from '@/components/ui/status-indicator';
import { MetricCard } from '@/components/ui/metric-card';
import dayjs from 'dayjs';

const { TabPane } = Tabs;

interface PluginDetailProps {
  open: boolean;
  pluginId: string | null;
  plugin?: PluginState;
  config?: PluginConfig;
  data?: PluginData[];
  logs?: PluginLog[];
  onClose: () => void;
  onAction?: (action: string, pluginId: string) => void;
}

// 模拟日志数据
const mockLogs: PluginLog[] = [
  {
    id: '1',
    plugin_id: 'modbus-001',
    level: 'info',
    message: '插件启动成功',
    timestamp: Date.now() - 300000,
    context: { startup_time: '2.3s' },
  },
  {
    id: '2',
    plugin_id: 'modbus-001',
    level: 'warning',
    message: '连接超时，正在重试',
    timestamp: Date.now() - 180000,
    context: { retry_count: 1 },
  },
  {
    id: '3',
    plugin_id: 'modbus-001',
    level: 'info',
    message: '数据读取成功',
    timestamp: Date.now() - 60000,
    context: { data_count: 10 },
  },
];

export const PluginDetail: React.FC<PluginDetailProps> = ({
  open,
  pluginId,
  plugin,
  config,
  data = [],
  logs = mockLogs,
  onClose,
  onAction,
}) => {
  const [activeTab, setActiveTab] = useState('overview');

  if (!plugin || !pluginId) {
    return null;
  }

  const handleAction = (action: string) => {
    onAction?.(action, pluginId);
  };

  // 概览标签页
  const OverviewTab = () => (
    <div className="space-y-4">
      {/* 基本信息 */}
      <Card title="基本信息" size="small">
        <Descriptions column={2} size="small">
          <Descriptions.Item label="插件ID">{plugin.plugin_id}</Descriptions.Item>
          <Descriptions.Item label="插件名称">
            {config?.name || plugin.plugin_id}
          </Descriptions.Item>
          <Descriptions.Item label="插件类型">{plugin.plugin_type}</Descriptions.Item>
          <Descriptions.Item label="协议类型">
            {config?.protocol || '-'}
          </Descriptions.Item>
          <Descriptions.Item label="当前状态">
            <StatusIndicator status={plugin.status} showText />
          </Descriptions.Item>
          <Descriptions.Item label="是否启用">
            <Tag color={config?.enabled ? 'green' : 'red'}>
              {config?.enabled ? '已启用' : '已禁用'}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="创建时间">
            {config?.created_at ? dayjs(config.created_at).format('YYYY-MM-DD HH:mm:ss') : '-'}
          </Descriptions.Item>
          <Descriptions.Item label="更新时间">
            {dayjs(plugin.timestamp).format('YYYY-MM-DD HH:mm:ss')}
          </Descriptions.Item>
        </Descriptions>
      </Card>

      {/* 性能指标 */}
      {plugin.details && (
        <Card title="性能指标" size="small">
          <Row gutter={16}>
            <Col span={8}>
              <MetricCard
                title="内存使用"
                value={plugin.details.memory_usage || '0 MB'}
                color="blue"
                size="small"
              />
            </Col>
            <Col span={8}>
              <MetricCard
                title="CPU使用率"
                value={plugin.details.cpu_usage || '0%'}
                color="green"
                size="small"
              />
            </Col>
            <Col span={8}>
              <MetricCard
                title="运行时间"
                value={plugin.details.uptime ? 
                  dayjs.duration(plugin.details.uptime).humanize() : '0分钟'}
                color="purple"
                size="small"
              />
            </Col>
          </Row>
          
          <Row gutter={16} className="mt-4">
            <Col span={12}>
              <Statistic
                title="成功次数"
                value={plugin.details.success_count || 0}
                valueStyle={{ color: '#3f8600' }}
              />
            </Col>
            <Col span={12}>
              <Statistic
                title="错误次数"
                value={plugin.details.error_count || 0}
                valueStyle={{ color: '#cf1322' }}
              />
            </Col>
          </Row>
        </Card>
      )}

      {/* 最近活动 */}
      <Card title="最近活动" size="small">
        <Timeline size="small">
          {logs.slice(0, 5).map((log) => (
            <Timeline.Item
              key={log.id}
              color={
                log.level === 'error' ? 'red' :
                log.level === 'warning' ? 'orange' : 'blue'
              }
            >
              <div className="flex justify-between items-start">
                <div>
                  <div className="text-sm">{log.message}</div>
                  <div className="text-xs text-gray-500">
                    {dayjs(log.timestamp).format('MM-DD HH:mm:ss')}
                  </div>
                </div>
                <Tag size="small" color={
                  log.level === 'error' ? 'red' :
                  log.level === 'warning' ? 'orange' : 'blue'
                }>
                  {log.level.toUpperCase()}
                </Tag>
              </div>
            </Timeline.Item>
          ))}
        </Timeline>
      </Card>
    </div>
  );

  // 配置标签页
  const ConfigTab = () => (
    <Card title="插件配置" size="small">
      {config ? (
        <pre className="bg-gray-50 p-4 rounded text-sm overflow-auto">
          {JSON.stringify(config.config, null, 2)}
        </pre>
      ) : (
        <Alert message="暂无配置信息" type="info" />
      )}
    </Card>
  );

  // 数据标签页
  const DataTab = () => {
    const columns = [
      {
        title: '时间',
        dataIndex: 'timestamp',
        key: 'timestamp',
        width: 150,
        render: (timestamp: number) => 
          dayjs(timestamp).format('MM-DD HH:mm:ss'),
      },
      {
        title: '数据类型',
        dataIndex: 'data_type',
        key: 'data_type',
        width: 120,
      },
      {
        title: '数据内容',
        dataIndex: 'data',
        key: 'data',
        render: (data: any) => (
          <div className="max-w-xs truncate">
            {typeof data === 'object' ? JSON.stringify(data) : String(data)}
          </div>
        ),
      },
    ];

    return (
      <Card title="数据记录" size="small">
        <Table
          columns={columns}
          dataSource={data}
          rowKey={(record) => `${record.timestamp}-${record.data_type}`}
          size="small"
          pagination={{ pageSize: 10 }}
          scroll={{ x: 400 }}
        />
      </Card>
    );
  };

  // 日志标签页
  const LogsTab = () => {
    const columns = [
      {
        title: '时间',
        dataIndex: 'timestamp',
        key: 'timestamp',
        width: 150,
        render: (timestamp: number) => 
          dayjs(timestamp).format('MM-DD HH:mm:ss'),
      },
      {
        title: '级别',
        dataIndex: 'level',
        key: 'level',
        width: 80,
        render: (level: string) => (
          <Tag color={
            level === 'error' ? 'red' :
            level === 'warning' ? 'orange' : 'blue'
          }>
            {level.toUpperCase()}
          </Tag>
        ),
      },
      {
        title: '消息',
        dataIndex: 'message',
        key: 'message',
      },
    ];

    return (
      <Card title="运行日志" size="small">
        <Table
          columns={columns}
          dataSource={logs}
          rowKey="id"
          size="small"
          pagination={{ pageSize: 10 }}
        />
      </Card>
    );
  };

  return (
    <Drawer
      title={
        <div className="flex items-center justify-between">
          <div>
            <span className="text-lg font-medium">
              {config?.name || plugin.plugin_id}
            </span>
            <div className="text-sm text-gray-500">
              插件详情
            </div>
          </div>
          <StatusIndicator status={plugin.status} showText />
        </div>
      }
      placement="right"
      width={800}
      open={open}
      onClose={onClose}
      extra={
        <Space>
          <Button
            icon={<ReloadOutlined />}
            onClick={() => handleAction('restart')}
            size="small"
          >
            重启
          </Button>
          {plugin.status === 'running' ? (
            <Button
              icon={<PauseCircleOutlined />}
              onClick={() => handleAction('stop')}
              size="small"
            >
              停止
            </Button>
          ) : (
            <Button
              icon={<PlayCircleOutlined />}
              onClick={() => handleAction('start')}
              size="small"
              type="primary"
            >
              启动
            </Button>
          )}
          <Button
            icon={<SettingOutlined />}
            onClick={() => handleAction('configure')}
            size="small"
          >
            配置
          </Button>
        </Space>
      }
    >
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="概览" key="overview">
          <OverviewTab />
        </TabPane>
        <TabPane tab="配置" key="config">
          <ConfigTab />
        </TabPane>
        <TabPane tab="数据" key="data">
          <DataTab />
        </TabPane>
        <TabPane tab="日志" key="logs">
          <LogsTab />
        </TabPane>
      </Tabs>
    </Drawer>
  );
};
