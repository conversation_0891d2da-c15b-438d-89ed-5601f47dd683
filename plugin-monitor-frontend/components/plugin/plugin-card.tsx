'use client';

import React from 'react';
import { Card, Tag, Button, Dropdown, Space, Typography } from 'antd';
import {
  MoreOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  ReloadOutlined,
  SettingOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import { PluginState, PluginConfig, PluginStatus, PluginType } from '@/lib/types/plugin';
import { StatusIndicator } from '@/components/ui/status-indicator';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';

dayjs.extend(relativeTime);

const { Text, Title } = Typography;

interface PluginCardProps {
  plugin: PluginState;
  config?: PluginConfig;
  onAction?: (action: string, pluginId: string) => void;
  className?: string;
}

const typeColors = {
  [PluginType.INBOUND]: 'blue',
  [PluginType.OUTBOUND]: 'green',
  [PluginType.RULE]: 'orange',
  [PluginType.FLOW]: 'purple',
};

export const PluginCard: React.FC<PluginCardProps> = ({
  plugin,
  config,
  onAction,
  className,
}) => {
  const handleMenuClick = (key: string) => {
    onAction?.(key, plugin.plugin_id);
  };

  const menuItems = [
    {
      key: 'start',
      label: '启动',
      icon: <PlayCircleOutlined />,
      disabled: plugin.status === PluginStatus.RUNNING,
    },
    {
      key: 'stop',
      label: '停止',
      icon: <PauseCircleOutlined />,
      disabled: plugin.status === PluginStatus.STOPPED,
    },
    {
      key: 'restart',
      label: '重启',
      icon: <ReloadOutlined />,
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'configure',
      label: '配置',
      icon: <SettingOutlined />,
    },
    {
      key: 'delete',
      label: '删除',
      icon: <DeleteOutlined />,
      danger: true,
    },
  ];

  const getPluginTypeText = (type: string) => {
    switch (type.toLowerCase()) {
      case 'inbound':
        return '输入插件';
      case 'outbound':
        return '输出插件';
      case 'rule':
        return '规则插件';
      case 'flow':
        return '流程插件';
      default:
        return type;
    }
  };

  const formatUptime = (timestamp: number) => {
    return dayjs(timestamp).fromNow();
  };

  return (
    <Card
      className={`plugin-card ${className || ''}`}
      size="small"
      actions={[
        <Button
          key="details"
          type="text"
          size="small"
          onClick={() => handleMenuClick('details')}
        >
          详情
        </Button>,
        <Dropdown
          key="more"
          menu={{ items: menuItems, onClick: ({ key }) => handleMenuClick(key) }}
          trigger={['click']}
        >
          <Button type="text" size="small" icon={<MoreOutlined />} />
        </Dropdown>,
      ]}
    >
      <div className="space-y-3">
        {/* 头部信息 */}
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <Title level={5} className="!mb-1 truncate">
              {config?.name || plugin.plugin_id}
            </Title>
            <Text type="secondary" className="text-xs">
              ID: {plugin.plugin_id}
            </Text>
          </div>
          <StatusIndicator
            status={plugin.status}
            tooltip={`状态: ${plugin.status}`}
          />
        </div>

        {/* 类型和协议标签 */}
        <div className="flex flex-wrap gap-1">
          <Tag color={typeColors[plugin.plugin_type as PluginType] || 'default'}>
            {getPluginTypeText(plugin.plugin_type)}
          </Tag>
          {config?.protocol && (
            <Tag>{config.protocol.toUpperCase()}</Tag>
          )}
        </div>

        {/* 状态详情 */}
        {plugin.details && (
          <div className="space-y-1">
            {plugin.details.memory_usage && (
              <div className="flex justify-between text-xs">
                <Text type="secondary">内存使用:</Text>
                <Text>{plugin.details.memory_usage}</Text>
              </div>
            )}
            {plugin.details.cpu_usage && (
              <div className="flex justify-between text-xs">
                <Text type="secondary">CPU使用:</Text>
                <Text>{plugin.details.cpu_usage}</Text>
              </div>
            )}
            {plugin.details.last_activity && (
              <div className="flex justify-between text-xs">
                <Text type="secondary">最后活动:</Text>
                <Text>{plugin.details.last_activity}</Text>
              </div>
            )}
            {plugin.details.error_count !== undefined && (
              <div className="flex justify-between text-xs">
                <Text type="secondary">错误次数:</Text>
                <Text type={plugin.details.error_count > 0 ? 'danger' : 'secondary'}>
                  {plugin.details.error_count}
                </Text>
              </div>
            )}
          </div>
        )}

        {/* 时间信息 */}
        <div className="pt-2 border-t border-gray-100">
          <Text type="secondary" className="text-xs">
            更新时间: {formatUptime(plugin.timestamp)}
          </Text>
        </div>
      </div>
    </Card>
  );
};
