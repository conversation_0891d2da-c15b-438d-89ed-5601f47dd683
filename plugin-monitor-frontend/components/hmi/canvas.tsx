'use client';

import React, { useState, useRef, useCallback } from 'react';
import { useDrop } from 'react-dnd';
import { Button, message } from 'antd';
import { 
  ZoomInOutlined, 
  ZoomOutOutlined, 
  BorderOutlined,
  DragOutlined 
} from '@ant-design/icons';
import { DraggableWidget, WidgetPosition } from './draggable-widget';
import clsx from 'clsx';

export interface CanvasWidget {
  id: string;
  type: string;
  position: WidgetPosition;
  config: any;
  locked?: boolean;
}

export interface CanvasProps {
  widgets: CanvasWidget[];
  selectedWidgetId?: string;
  isPreviewMode?: boolean;
  showGrid?: boolean;
  gridSize?: number;
  zoom?: number;
  onWidgetAdd?: (widget: Omit<CanvasWidget, 'id'>) => void;
  onWidgetUpdate?: (id: string, updates: Partial<CanvasWidget>) => void;
  onWidgetDelete?: (id: string) => void;
  onWidgetSelect?: (id: string | undefined) => void;
  onWidgetCopy?: (id: string) => void;
  onZoomChange?: (zoom: number) => void;
  renderWidget?: (widget: CanvasWidget) => React.ReactNode;
}

export const Canvas: React.FC<CanvasProps> = ({
  widgets,
  selectedWidgetId,
  isPreviewMode = false,
  showGrid = true,
  gridSize = 10,
  zoom = 1,
  onWidgetAdd,
  onWidgetUpdate,
  onWidgetDelete,
  onWidgetSelect,
  onWidgetCopy,
  onZoomChange,
  renderWidget,
}) => {
  const canvasRef = useRef<HTMLDivElement>(null);
  const [isPanning, setIsPanning] = useState(false);
  const [panOffset, setPanOffset] = useState({ x: 0, y: 0 });

  // 拖放功能
  const [{ isOver, canDrop }, drop] = useDrop({
    accept: ['WIDGET_LIBRARY_ITEM', 'WIDGET'],
    drop: (item: any, monitor) => {
      if (!canvasRef.current) return;

      const canvasRect = canvasRef.current.getBoundingClientRect();
      const dropPosition = monitor.getClientOffset();
      
      if (!dropPosition) return;

      const x = (dropPosition.x - canvasRect.left - panOffset.x) / zoom;
      const y = (dropPosition.y - canvasRect.top - panOffset.y) / zoom;

      if (item.id && widgets.find(w => w.id === item.id)) {
        // 移动现有组件
        onWidgetUpdate?.(item.id, {
          position: {
            ...item.position,
            x: Math.max(0, Math.round(x / gridSize) * gridSize),
            y: Math.max(0, Math.round(y / gridSize) * gridSize),
          }
        });
      } else {
        // 添加新组件
        const newWidget: Omit<CanvasWidget, 'id'> = {
          type: item.type || item.id,
          position: {
            x: Math.max(0, Math.round(x / gridSize) * gridSize),
            y: Math.max(0, Math.round(y / gridSize) * gridSize),
            width: item.defaultWidth || 200,
            height: item.defaultHeight || 100,
          },
          config: item.defaultConfig || {},
        };
        onWidgetAdd?.(newWidget);
      }
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop(),
    }),
  });

  // 处理组件位置变化
  const handleWidgetPositionChange = useCallback((id: string, position: WidgetPosition) => {
    onWidgetUpdate?.(id, { position });
  }, [onWidgetUpdate]);

  // 处理组件选择
  const handleWidgetSelect = useCallback((id: string) => {
    onWidgetSelect?.(id);
  }, [onWidgetSelect]);

  // 处理组件删除
  const handleWidgetDelete = useCallback((id: string) => {
    onWidgetDelete?.(id);
    message.success('组件已删除');
  }, [onWidgetDelete]);

  // 处理组件复制
  const handleWidgetCopy = useCallback((id: string) => {
    onWidgetCopy?.(id);
    message.success('组件已复制');
  }, [onWidgetCopy]);

  // 处理组件锁定切换
  const handleWidgetLockToggle = useCallback((id: string) => {
    const widget = widgets.find(w => w.id === id);
    if (widget) {
      onWidgetUpdate?.(id, { locked: !widget.locked });
      message.success(widget.locked ? '组件已解锁' : '组件已锁定');
    }
  }, [widgets, onWidgetUpdate]);

  // 处理画布点击
  const handleCanvasClick = useCallback((e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onWidgetSelect?.(undefined);
    }
  }, [onWidgetSelect]);

  // 处理缩放
  const handleZoom = useCallback((delta: number) => {
    const newZoom = Math.max(0.1, Math.min(3, zoom + delta));
    onZoomChange?.(newZoom);
  }, [zoom, onZoomChange]);

  // 处理平移
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (e.button === 1 || (e.button === 0 && e.ctrlKey)) { // 中键或Ctrl+左键
      e.preventDefault();
      setIsPanning(true);
      
      const startX = e.clientX;
      const startY = e.clientY;
      const startOffset = { ...panOffset };

      const handleMouseMove = (e: MouseEvent) => {
        setPanOffset({
          x: startOffset.x + (e.clientX - startX),
          y: startOffset.y + (e.clientY - startY),
        });
      };

      const handleMouseUp = () => {
        setIsPanning(false);
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };

      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }
  }, [panOffset]);

  // 渲染网格
  const renderGrid = () => {
    if (!showGrid || isPreviewMode) return null;

    const gridPattern = `
      <defs>
        <pattern id="grid" width="${gridSize * zoom}" height="${gridSize * zoom}" patternUnits="userSpaceOnUse">
          <path d="M ${gridSize * zoom} 0 L 0 0 0 ${gridSize * zoom}" fill="none" stroke="#e0e0e0" stroke-width="1"/>
        </pattern>
      </defs>
      <rect width="100%" height="100%" fill="url(#grid)" />
    `;

    return (
      <svg 
        className="absolute inset-0 pointer-events-none"
        style={{ transform: `translate(${panOffset.x}px, ${panOffset.y}px)` }}
      >
        <defs>
          <pattern 
            id="grid" 
            width={gridSize * zoom} 
            height={gridSize * zoom} 
            patternUnits="userSpaceOnUse"
          >
            <path 
              d={`M ${gridSize * zoom} 0 L 0 0 0 ${gridSize * zoom}`} 
              fill="none" 
              stroke="#e0e0e0" 
              strokeWidth="1"
            />
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#grid)" />
      </svg>
    );
  };

  // 默认组件渲染器
  const defaultRenderWidget = (widget: CanvasWidget) => {
    return (
      <div className="w-full h-full bg-white border rounded shadow-sm flex items-center justify-center">
        <div className="text-center">
          <div className="text-2xl mb-2">📊</div>
          <div className="text-sm font-medium">{widget.type}</div>
        </div>
      </div>
    );
  };

  return (
    <div className="relative w-full h-full overflow-hidden bg-gray-50">
      {/* 工具栏 */}
      {!isPreviewMode && (
        <div className="absolute top-4 right-4 z-20 flex space-x-2 bg-white rounded shadow-md p-2">
          <Button
            size="small"
            icon={<ZoomOutOutlined />}
            onClick={() => handleZoom(-0.1)}
            disabled={zoom <= 0.1}
          />
          <span className="px-2 py-1 text-sm">{Math.round(zoom * 100)}%</span>
          <Button
            size="small"
            icon={<ZoomInOutlined />}
            onClick={() => handleZoom(0.1)}
            disabled={zoom >= 3}
          />
          <Button
            size="small"
            icon={<BorderOutlined />}
            type={showGrid ? 'primary' : 'default'}
            onClick={() => {/* TODO: 切换网格显示 */}}
          />
        </div>
      )}

      {/* 画布 */}
      <div
        ref={(node) => {
          canvasRef.current = node;
          drop(node);
        }}
        className={clsx(
          'w-full h-full relative',
          {
            'cursor-grab': !isPanning,
            'cursor-grabbing': isPanning,
            'bg-blue-50': isOver && canDrop,
          }
        )}
        onClick={handleCanvasClick}
        onMouseDown={handleMouseDown}
        style={{
          transform: `scale(${zoom}) translate(${panOffset.x / zoom}px, ${panOffset.y / zoom}px)`,
          transformOrigin: '0 0',
        }}
      >
        {renderGrid()}

        {/* 组件 */}
        {widgets.map((widget) => (
          <DraggableWidget
            key={widget.id}
            id={widget.id}
            type={widget.type}
            position={widget.position}
            selected={selectedWidgetId === widget.id}
            locked={widget.locked}
            onPositionChange={handleWidgetPositionChange}
            onSelect={handleWidgetSelect}
            onDelete={handleWidgetDelete}
            onCopy={handleWidgetCopy}
            onLockToggle={handleWidgetLockToggle}
            isPreviewMode={isPreviewMode}
            gridSize={gridSize}
          >
            {renderWidget ? renderWidget(widget) : defaultRenderWidget(widget)}
          </DraggableWidget>
        ))}

        {/* 拖放提示 */}
        {isOver && canDrop && (
          <div className="absolute inset-0 border-2 border-dashed border-blue-400 bg-blue-50 bg-opacity-50 flex items-center justify-center">
            <div className="text-blue-600 text-lg font-medium">
              释放以添加组件
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
