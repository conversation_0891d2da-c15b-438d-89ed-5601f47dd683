'use client';

import React from 'react';
import { useDrag } from 'react-dnd';
import { Card, Tabs, Input, Collapse } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import clsx from 'clsx';

const { TabPane } = Tabs;
const { Panel } = Collapse;

export interface WidgetLibraryItem {
  id: string;
  name: string;
  icon: string;
  category: string;
  description: string;
  defaultWidth: number;
  defaultHeight: number;
  defaultConfig: any;
  preview?: React.ReactNode;
}

export interface WidgetLibraryProps {
  items: WidgetLibraryItem[];
  searchText?: string;
  onSearchChange?: (text: string) => void;
  selectedCategory?: string;
  onCategoryChange?: (category: string) => void;
}

// 拖拽组件项
const DraggableWidgetItem: React.FC<{
  item: WidgetLibraryItem;
}> = ({ item }) => {
  const [{ isDragging }, drag] = useDrag({
    type: 'WIDGET_LIBRARY_ITEM',
    item: {
      type: item.id,
      defaultWidth: item.defaultWidth,
      defaultHeight: item.defaultHeight,
      defaultConfig: item.defaultConfig,
    },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  return (
    <Card
      ref={drag}
      size="small"
      className={clsx(
        'cursor-move hover:shadow-md transition-all duration-200 mb-2',
        {
          'opacity-50': isDragging,
        }
      )}
      bodyStyle={{ padding: '12px' }}
    >
      <div className="flex items-center space-x-3">
        <div className="text-2xl">{item.icon}</div>
        <div className="flex-1 min-w-0">
          <div className="font-medium text-sm truncate">{item.name}</div>
          <div className="text-xs text-gray-500 truncate">{item.description}</div>
        </div>
      </div>
      {item.preview && (
        <div className="mt-2 p-2 bg-gray-50 rounded">
          {item.preview}
        </div>
      )}
    </Card>
  );
};

export const WidgetLibrary: React.FC<WidgetLibraryProps> = ({
  items,
  searchText = '',
  onSearchChange,
  selectedCategory = 'all',
  onCategoryChange,
}) => {
  // 分类组件
  const categories = [
    { key: 'all', label: '全部组件' },
    { key: 'display', label: '显示组件' },
    { key: 'chart', label: '图表组件' },
    { key: 'control', label: '控制组件' },
    { key: 'container', label: '容器组件' },
    { key: 'decoration', label: '装饰组件' },
  ];

  // 过滤组件
  const filteredItems = items.filter(item => {
    const matchesSearch = !searchText || 
      item.name.toLowerCase().includes(searchText.toLowerCase()) ||
      item.description.toLowerCase().includes(searchText.toLowerCase());
    
    const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  // 按分类分组
  const groupedItems = categories.reduce((acc, category) => {
    if (category.key === 'all') return acc;
    
    acc[category.key] = filteredItems.filter(item => item.category === category.key);
    return acc;
  }, {} as Record<string, WidgetLibraryItem[]>);

  return (
    <div className="h-full flex flex-col">
      {/* 搜索框 */}
      <div className="p-4 border-b">
        <Input
          placeholder="搜索组件..."
          prefix={<SearchOutlined />}
          value={searchText}
          onChange={(e) => onSearchChange?.(e.target.value)}
          allowClear
        />
      </div>

      {/* 分类标签 */}
      <div className="flex-1 overflow-auto">
        <Tabs
          activeKey={selectedCategory}
          onChange={onCategoryChange}
          size="small"
          className="px-2"
        >
          {categories.map(category => (
            <TabPane tab={category.label} key={category.key}>
              <div className="px-2 pb-4">
                {category.key === 'all' ? (
                  // 全部组件 - 按分类折叠显示
                  <Collapse ghost>
                    {categories.slice(1).map(cat => {
                      const categoryItems = groupedItems[cat.key] || [];
                      if (categoryItems.length === 0) return null;
                      
                      return (
                        <Panel 
                          header={`${cat.label} (${categoryItems.length})`} 
                          key={cat.key}
                        >
                          {categoryItems.map(item => (
                            <DraggableWidgetItem key={item.id} item={item} />
                          ))}
                        </Panel>
                      );
                    })}
                  </Collapse>
                ) : (
                  // 特定分类组件
                  <div>
                    {(groupedItems[category.key] || []).map(item => (
                      <DraggableWidgetItem key={item.id} item={item} />
                    ))}
                    {(groupedItems[category.key] || []).length === 0 && (
                      <div className="text-center text-gray-500 py-8">
                        {searchText ? '没有找到匹配的组件' : '该分类暂无组件'}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </TabPane>
          ))}
        </Tabs>
      </div>
    </div>
  );
};

// 默认组件库数据
export const defaultWidgetLibrary: WidgetLibraryItem[] = [
  // 显示组件
  {
    id: 'text-display',
    name: '文本显示',
    icon: '📝',
    category: 'display',
    description: '显示静态或动态文本',
    defaultWidth: 200,
    defaultHeight: 60,
    defaultConfig: {
      text: '文本内容',
      fontSize: 14,
      color: '#333333',
      align: 'left',
    },
  },
  {
    id: 'number-display',
    name: '数字显示',
    icon: '🔢',
    category: 'display',
    description: '显示数值数据',
    defaultWidth: 150,
    defaultHeight: 80,
    defaultConfig: {
      value: 0,
      unit: '',
      precision: 2,
      fontSize: 24,
      color: '#1890ff',
    },
  },
  {
    id: 'status-indicator',
    name: '状态指示器',
    icon: '🚦',
    category: 'display',
    description: '显示状态信息',
    defaultWidth: 120,
    defaultHeight: 60,
    defaultConfig: {
      status: 'normal',
      showText: true,
      size: 'default',
    },
  },
  
  // 图表组件
  {
    id: 'line-chart',
    name: '折线图',
    icon: '📈',
    category: 'chart',
    description: '时间序列折线图',
    defaultWidth: 400,
    defaultHeight: 300,
    defaultConfig: {
      title: '折线图',
      showLegend: true,
      showGrid: true,
    },
  },
  {
    id: 'gauge-chart',
    name: '仪表盘',
    icon: '⏲️',
    category: 'chart',
    description: '圆形仪表盘图表',
    defaultWidth: 250,
    defaultHeight: 250,
    defaultConfig: {
      min: 0,
      max: 100,
      value: 50,
      unit: '%',
      title: '仪表盘',
    },
  },
  {
    id: 'bar-chart',
    name: '柱状图',
    icon: '📊',
    category: 'chart',
    description: '数据对比柱状图',
    defaultWidth: 400,
    defaultHeight: 300,
    defaultConfig: {
      title: '柱状图',
      showLegend: true,
      showGrid: true,
    },
  },
  
  // 控制组件
  {
    id: 'button',
    name: '按钮',
    icon: '🔘',
    category: 'control',
    description: '交互按钮',
    defaultWidth: 100,
    defaultHeight: 40,
    defaultConfig: {
      text: '按钮',
      type: 'primary',
      size: 'default',
      action: 'none',
    },
  },
  {
    id: 'switch',
    name: '开关',
    icon: '🔄',
    category: 'control',
    description: '开关控制',
    defaultWidth: 80,
    defaultHeight: 40,
    defaultConfig: {
      checked: false,
      size: 'default',
      disabled: false,
    },
  },
  
  // 容器组件
  {
    id: 'panel',
    name: '面板',
    icon: '📋',
    category: 'container',
    description: '容器面板',
    defaultWidth: 300,
    defaultHeight: 200,
    defaultConfig: {
      title: '面板标题',
      bordered: true,
      background: '#ffffff',
    },
  },
  
  // 装饰组件
  {
    id: 'image',
    name: '图片',
    icon: '🖼️',
    category: 'decoration',
    description: '显示图片',
    defaultWidth: 200,
    defaultHeight: 150,
    defaultConfig: {
      src: '',
      alt: '图片',
      fit: 'cover',
    },
  },
];
