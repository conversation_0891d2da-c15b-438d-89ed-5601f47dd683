'use client';

import React, { useEffect, createContext, useContext } from 'react';
import { ConfigProvider, theme as antdTheme } from 'antd';
import { useConfigStore } from '@/lib/stores/config-store';

interface ThemeContextType {
  theme: 'light' | 'dark' | 'auto';
  isDark: boolean;
  toggleTheme: () => void;
  setTheme: (theme: 'light' | 'dark' | 'auto') => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: React.ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const { theme, customTheme, setTheme, applyTheme } = useConfigStore();
  const [isDark, setIsDark] = React.useState(false);

  // 检测系统主题
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleChange = (e: MediaQueryListEvent) => {
      if (theme === 'auto') {
        setIsDark(e.matches);
      }
    };

    // 初始检测
    if (theme === 'auto') {
      setIsDark(mediaQuery.matches);
    } else {
      setIsDark(theme === 'dark');
    }

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [theme]);

  // 应用主题
  useEffect(() => {
    applyTheme();
    
    // 更新body类名
    document.body.className = document.body.className.replace(/theme-\w+/g, '');
    document.body.classList.add(`theme-${isDark ? 'dark' : 'light'}`);
  }, [theme, isDark, customTheme, applyTheme]);

  const toggleTheme = () => {
    const newTheme = isDark ? 'light' : 'dark';
    setTheme(newTheme);
  };

  // Ant Design 主题配置
  const antdThemeConfig = {
    algorithm: isDark ? antdTheme.darkAlgorithm : antdTheme.defaultAlgorithm,
    token: {
      colorPrimary: customTheme.primaryColor || '#1890ff',
      colorSuccess: customTheme.successColor || '#52c41a',
      colorWarning: customTheme.warningColor || '#faad14',
      colorError: customTheme.errorColor || '#ff4d4f',
      borderRadius: 6,
      wireframe: false,
      // 暗色主题特定配置
      ...(isDark && {
        colorBgContainer: '#1f1f1f',
        colorBgElevated: '#262626',
        colorBgLayout: '#141414',
        colorText: '#ffffff',
        colorTextSecondary: '#a6a6a6',
        colorBorder: '#434343',
      }),
    },
    components: {
      Layout: {
        headerBg: isDark ? '#1f1f1f' : '#ffffff',
        siderBg: isDark ? '#262626' : '#fafafa',
        bodyBg: isDark ? '#141414' : '#f5f5f5',
      },
      Menu: {
        itemBg: 'transparent',
        itemSelectedBg: isDark ? '#1890ff20' : '#e0f2fe',
        itemSelectedColor: customTheme.primaryColor || '#1890ff',
        itemHoverBg: isDark ? '#ffffff10' : '#f5f5f5',
      },
      Card: {
        colorBgContainer: isDark ? '#1f1f1f' : '#ffffff',
        colorBorderSecondary: isDark ? '#434343' : '#f0f0f0',
      },
      Button: {
        colorPrimary: customTheme.primaryColor || '#1890ff',
        colorPrimaryHover: customTheme.primaryColor ? 
          adjustColor(customTheme.primaryColor, 10) : '#40a9ff',
      },
    },
  };

  const contextValue: ThemeContextType = {
    theme,
    isDark,
    toggleTheme,
    setTheme,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      <ConfigProvider theme={antdThemeConfig}>
        {children}
      </ConfigProvider>
    </ThemeContext.Provider>
  );
};

// 颜色调整工具函数
function adjustColor(color: string, amount: number): string {
  const usePound = color[0] === '#';
  const col = usePound ? color.slice(1) : color;
  
  const num = parseInt(col, 16);
  let r = (num >> 16) + amount;
  let g = (num >> 8 & 0x00FF) + amount;
  let b = (num & 0x0000FF) + amount;
  
  r = r > 255 ? 255 : r < 0 ? 0 : r;
  g = g > 255 ? 255 : g < 0 ? 0 : g;
  b = b > 255 ? 255 : b < 0 ? 0 : b;
  
  return (usePound ? '#' : '') + (r << 16 | g << 8 | b).toString(16).padStart(6, '0');
}

// 主题切换按钮组件
export const ThemeToggle: React.FC<{
  className?: string;
}> = ({ className }) => {
  const { theme, isDark, toggleTheme } = useTheme();
  
  return (
    <button
      onClick={toggleTheme}
      className={`p-2 rounded-lg transition-colors ${
        isDark 
          ? 'bg-gray-800 text-yellow-400 hover:bg-gray-700' 
          : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
      } ${className}`}
      title={`切换到${isDark ? '亮色' : '暗色'}主题`}
    >
      {isDark ? '🌞' : '🌙'}
    </button>
  );
};

// CSS 变量定义
export const themeVariables = `
  :root {
    --color-primary: #1890ff;
    --color-success: #52c41a;
    --color-warning: #faad14;
    --color-error: #ff4d4f;
    --color-text: #333333;
    --color-text-secondary: #666666;
    --color-bg: #ffffff;
    --color-bg-secondary: #f5f5f5;
    --color-border: #d9d9d9;
    --border-radius: 6px;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  }

  .theme-dark {
    --color-text: #ffffff;
    --color-text-secondary: #a6a6a6;
    --color-bg: #1f1f1f;
    --color-bg-secondary: #262626;
    --color-border: #434343;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5);
  }

  .theme-light {
    /* 使用默认的亮色主题变量 */
  }

  /* 过渡动画 */
  * {
    transition: background-color 0.2s ease, color 0.2s ease, border-color 0.2s ease;
  }

  /* 自定义滚动条 */
  .theme-dark ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .theme-dark ::-webkit-scrollbar-track {
    background: #262626;
  }

  .theme-dark ::-webkit-scrollbar-thumb {
    background: #434343;
    border-radius: 3px;
  }

  .theme-dark ::-webkit-scrollbar-thumb:hover {
    background: #595959;
  }
`;

// 主题样式注入组件
export const ThemeStyles: React.FC = () => {
  return (
    <style jsx global>{themeVariables}</style>
  );
};
