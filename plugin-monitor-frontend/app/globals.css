@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局样式重置 */
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Antd 组件样式覆盖 */
.ant-layout {
  min-height: 100vh;
}

.ant-layout-sider {
  box-shadow: 2px 0 8px 0 rgba(29, 35, 41, 0.05);
}

.ant-menu-item {
  transition: all 0.2s ease;
}

.ant-menu-item:hover {
  transform: translateX(2px);
}

/* 状态指示器样式 */
.status-indicator {
  @apply inline-flex items-center justify-center w-3 h-3 rounded-full;
}

.status-running {
  @apply bg-success-500;
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.status-error {
  @apply bg-error-500;
}

.status-idle {
  @apply bg-warning-500;
}

.status-stopped {
  @apply bg-gray-400;
}

/* 卡片悬停效果 */
.plugin-card {
  @apply transition-all duration-200 ease-in-out;
}

.plugin-card:hover {
  @apply shadow-lg transform -translate-y-1;
}

/* 拖拽相关样式 */
.draggable-item {
  @apply cursor-move transition-all duration-200;
}

.draggable-item:hover {
  @apply shadow-md;
}

.drop-zone {
  @apply border-2 border-dashed border-gray-300 rounded-lg;
  transition: all 0.2s ease;
}

.drop-zone.drag-over {
  @apply border-primary-500 bg-primary-50;
}

/* 图表容器样式 */
.chart-container {
  @apply w-full h-full;
}

.chart-loading {
  @apply flex items-center justify-center h-64;
}

/* 响应式工具类 */
@media (max-width: 768px) {
  .mobile-hidden {
    display: none !important;
  }
  
  .mobile-full {
    width: 100% !important;
  }
}

/* 动画类 */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 自定义工具提示样式 */
.custom-tooltip {
  @apply bg-gray-800 text-white px-2 py-1 rounded text-sm;
}

/* 加载状态样式 */
.loading-spinner {
  @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-500;
}
