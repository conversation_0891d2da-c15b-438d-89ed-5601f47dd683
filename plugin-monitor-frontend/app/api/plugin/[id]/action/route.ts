import { NextRequest, NextResponse } from 'next/server';
import { ApiResponse } from '@/lib/types/plugin';

interface ActionRequest {
  action: string;
  payload?: any;
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const pluginId = params.id;
    const body: ActionRequest = await request.json();

    if (!body.action) {
      return NextResponse.json(
        {
          success: false,
          error: '缺少必需参数: action',
          timestamp: Date.now(),
        },
        { status: 400 }
      );
    }

    // 验证插件ID
    if (!pluginId || pluginId.trim() === '') {
      return NextResponse.json(
        {
          success: false,
          error: '无效的插件ID',
          timestamp: Date.now(),
        },
        { status: 400 }
      );
    }

    // 验证操作类型
    const validActions = ['start', 'stop', 'restart', 'configure', 'delete'];
    if (!validActions.includes(body.action)) {
      return NextResponse.json(
        {
          success: false,
          error: `不支持的操作: ${body.action}`,
          timestamp: Date.now(),
        },
        { status: 400 }
      );
    }

    // 模拟执行操作
    const result = await executePluginAction(pluginId, body.action, body.payload);

    const response: ApiResponse<any> = {
      success: true,
      data: result,
      message: `插件 ${pluginId} 执行 ${body.action} 操作成功`,
      timestamp: Date.now(),
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Plugin action error:', error);
    
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : '执行插件操作失败',
      timestamp: Date.now(),
    };

    return NextResponse.json(response, { status: 500 });
  }
}

// 模拟插件操作执行
async function executePluginAction(
  pluginId: string,
  action: string,
  payload?: any
): Promise<any> {
  // 在实际项目中，这里应该调用 Beezer API
  const beezerApiUrl = process.env.BEEZER_API_URL || 'http://localhost:9999';
  
  try {
    const response = await fetch(`${beezerApiUrl}/api/plugin/${pluginId}/action`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ action, payload }),
    });

    if (!response.ok) {
      throw new Error(`Beezer API error: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    // 如果后端不可用，返回模拟结果
    console.warn('Beezer API not available, using mock response');
    
    return {
      plugin_id: pluginId,
      action,
      status: 'success',
      timestamp: Date.now(),
      details: {
        message: `模拟执行 ${action} 操作`,
        payload,
      },
    };
  }
}

// 支持的操作类型和描述
export const SUPPORTED_ACTIONS = {
  start: {
    description: '启动插件',
    requiresPayload: false,
  },
  stop: {
    description: '停止插件',
    requiresPayload: false,
  },
  restart: {
    description: '重启插件',
    requiresPayload: false,
  },
  configure: {
    description: '配置插件',
    requiresPayload: true,
  },
  delete: {
    description: '删除插件',
    requiresPayload: false,
  },
};
