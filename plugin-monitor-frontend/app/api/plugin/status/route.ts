import { NextRequest, NextResponse } from 'next/server';
import { ApiResponse, PluginState } from '@/lib/types/plugin';

// 模拟数据 - 实际项目中应该从后端API获取
const mockPluginStatus: PluginState[] = [
  {
    plugin_id: 'modbus-001',
    plugin_type: 'inbound',
    status: 'running',
    timestamp: Date.now(),
    details: {
      memory_usage: '45.2 MB',
      cpu_usage: '2.3%',
      last_activity: '2分钟前',
      error_count: 0,
      success_count: 1250,
      uptime: 3600000,
    },
  },
  {
    plugin_id: 'http-client-002',
    plugin_type: 'outbound',
    status: 'error',
    timestamp: Date.now(),
    details: {
      memory_usage: '32.1 MB',
      cpu_usage: '0.1%',
      last_activity: '5分钟前',
      error_count: 3,
      success_count: 890,
      uptime: 7200000,
    },
  },
  {
    plugin_id: 'mqtt-publisher-003',
    plugin_type: 'outbound',
    status: 'running',
    timestamp: Date.now(),
    details: {
      memory_usage: '28.7 MB',
      cpu_usage: '1.8%',
      last_activity: '刚刚',
      error_count: 0,
      success_count: 2340,
      uptime: 1800000,
    },
  },
  {
    plugin_id: 'data-filter-004',
    plugin_type: 'rule',
    status: 'idle',
    timestamp: Date.now(),
    details: {
      memory_usage: '15.3 MB',
      cpu_usage: '0.0%',
      last_activity: '10分钟前',
      error_count: 1,
      success_count: 567,
      uptime: 5400000,
    },
  },
  {
    plugin_id: 'websocket-005',
    plugin_type: 'inbound',
    status: 'stopped',
    timestamp: Date.now(),
    details: {
      memory_usage: '0 MB',
      cpu_usage: '0.0%',
      last_activity: '1小时前',
      error_count: 0,
      success_count: 0,
      uptime: 0,
    },
  },
];

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const pluginType = searchParams.get('plugin_type');
    const status = searchParams.get('status');

    let filteredData = mockPluginStatus;

    // 按插件类型过滤
    if (pluginType) {
      filteredData = filteredData.filter(plugin => plugin.plugin_type === pluginType);
    }

    // 按状态过滤
    if (status) {
      filteredData = filteredData.filter(plugin => plugin.status === status);
    }

    const response: ApiResponse<PluginState[]> = {
      success: true,
      data: filteredData,
      timestamp: Date.now(),
    };

    return NextResponse.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : '获取插件状态失败',
      timestamp: Date.now(),
    };

    return NextResponse.json(response, { status: 500 });
  }
}

// 在实际项目中，这里应该代理到后端API
async function fetchFromBeezerAPI(endpoint: string, options?: RequestInit) {
  const beezerApiUrl = process.env.BEEZER_API_URL || 'http://localhost:9999';
  const response = await fetch(`${beezerApiUrl}${endpoint}`, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      ...options?.headers,
    },
  });

  if (!response.ok) {
    throw new Error(`Beezer API error: ${response.status}`);
  }

  return response.json();
}
