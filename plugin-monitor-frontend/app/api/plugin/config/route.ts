import { NextRequest, NextResponse } from 'next/server';
import { ApiResponse, PluginConfig } from '@/lib/types/plugin';

// 模拟插件配置数据
const mockPluginConfigs: PluginConfig[] = [
  {
    id: 'modbus-001',
    type: 'inbound',
    protocol: 'modbus',
    name: 'Modbus TCP 采集器',
    enabled: true,
    config: {
      host: '*************',
      port: 502,
      slave_id: 1,
      registers: [
        { address: 40001, type: 'holding', count: 10 },
        { address: 30001, type: 'input', count: 5 },
      ],
      poll_interval: 1000,
    },
    created_at: '2024-01-15T10:30:00Z',
    updated_at: '2024-01-20T14:45:00Z',
  },
  {
    id: 'http-client-002',
    type: 'outbound',
    protocol: 'http_client',
    name: 'HTTP API 客户端',
    enabled: true,
    config: {
      base_url: 'https://api.example.com',
      timeout: 5000,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer token123',
      },
      endpoints: [
        { path: '/data', method: 'POST' },
        { path: '/status', method: 'GET' },
      ],
    },
    created_at: '2024-01-16T09:15:00Z',
    updated_at: '2024-01-19T16:20:00Z',
  },
  {
    id: 'mqtt-publisher-003',
    type: 'outbound',
    protocol: 'mqtt',
    name: 'MQTT 发布器',
    enabled: true,
    config: {
      broker_url: 'mqtt://localhost:1883',
      client_id: 'beezer_publisher_001',
      username: 'admin',
      password: 'password',
      topics: [
        { topic: 'sensors/temperature', qos: 1 },
        { topic: 'sensors/humidity', qos: 1 },
      ],
      keep_alive: 60,
    },
    created_at: '2024-01-17T11:00:00Z',
    updated_at: '2024-01-21T08:30:00Z',
  },
  {
    id: 'data-filter-004',
    type: 'rule',
    protocol: 'rule',
    name: '数据过滤规则',
    enabled: true,
    config: {
      rules: [
        {
          name: '温度范围检查',
          condition: 'temperature > 0 && temperature < 100',
          action: 'pass',
        },
        {
          name: '异常值过滤',
          condition: 'value == null || value == undefined',
          action: 'drop',
        },
      ],
      default_action: 'pass',
    },
    created_at: '2024-01-18T13:45:00Z',
    updated_at: '2024-01-22T10:15:00Z',
  },
  {
    id: 'websocket-005',
    type: 'inbound',
    protocol: 'websocket',
    name: 'WebSocket 服务器',
    enabled: false,
    config: {
      port: 8080,
      path: '/ws',
      max_connections: 100,
      heartbeat_interval: 30000,
      auth_required: true,
    },
    created_at: '2024-01-19T15:20:00Z',
    updated_at: '2024-01-23T12:00:00Z',
  },
];

export async function GET(request: NextRequest) {
  try {
    const response: ApiResponse<PluginConfig[]> = {
      success: true,
      data: mockPluginConfigs,
      timestamp: Date.now(),
    };

    return NextResponse.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : '获取插件配置失败',
      timestamp: Date.now(),
    };

    return NextResponse.json(response, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // 验证必需字段
    if (!body.name || !body.type || !body.protocol) {
      return NextResponse.json(
        {
          success: false,
          error: '缺少必需字段: name, type, protocol',
          timestamp: Date.now(),
        },
        { status: 400 }
      );
    }

    // 创建新配置
    const newConfig: PluginConfig = {
      id: `${body.protocol}-${Date.now()}`,
      type: body.type,
      protocol: body.protocol,
      name: body.name,
      enabled: body.enabled ?? true,
      config: body.config || {},
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    // 在实际项目中，这里应该保存到数据库或调用后端API
    mockPluginConfigs.push(newConfig);

    const response: ApiResponse<PluginConfig> = {
      success: true,
      data: newConfig,
      message: '插件配置创建成功',
      timestamp: Date.now(),
    };

    return NextResponse.json(response, { status: 201 });
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : '创建插件配置失败',
      timestamp: Date.now(),
    };

    return NextResponse.json(response, { status: 500 });
  }
}
