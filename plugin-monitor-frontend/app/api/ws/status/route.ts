import { NextRequest, NextResponse } from 'next/server';
import { ApiResponse } from '@/lib/types/plugin';

interface WebSocketStatus {
  connected: boolean;
  uptime: number;
  connections: number;
  lastHeartbeat: number;
  version: string;
}

export async function GET(request: NextRequest) {
  try {
    // 在实际项目中，这里应该检查真实的 WebSocket 服务状态
    const status = await getWebSocketStatus();

    const response: ApiResponse<WebSocketStatus> = {
      success: true,
      data: status,
      timestamp: Date.now(),
    };

    return NextResponse.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : '获取WebSocket状态失败',
      timestamp: Date.now(),
    };

    return NextResponse.json(response, { status: 500 });
  }
}

async function getWebSocketStatus(): Promise<WebSocketStatus> {
  const websocketUrl = process.env.WEBSOCKET_URL || 'ws://localhost:9999';
  
  try {
    // 尝试连接到 Beezer WebSocket 服务检查状态
    const healthCheckUrl = websocketUrl.replace('ws://', 'http://').replace('wss://', 'https://') + '/health';
    
    const response = await fetch(healthCheckUrl, {
      method: 'GET',
      timeout: 5000,
    });

    if (response.ok) {
      const data = await response.json();
      return {
        connected: true,
        uptime: data.uptime || 0,
        connections: data.connections || 0,
        lastHeartbeat: Date.now(),
        version: data.version || '1.0.0',
      };
    } else {
      throw new Error('WebSocket service not responding');
    }
  } catch (error) {
    // 如果无法连接到真实服务，返回模拟状态
    console.warn('WebSocket service not available, using mock status');
    
    return {
      connected: false,
      uptime: 0,
      connections: 0,
      lastHeartbeat: Date.now() - 30000, // 30秒前
      version: '1.0.0',
    };
  }
}
