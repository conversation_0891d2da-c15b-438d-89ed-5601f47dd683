import { NextRequest, NextResponse } from 'next/server';
import { ApiResponse, PaginatedResponse } from '@/lib/types/plugin';
import { DashboardConfig } from '@/lib/types/dashboard';

// 模拟仪表板配置数据
const mockDashboards: DashboardConfig[] = [
  {
    id: 'dashboard-001',
    name: '系统总览',
    description: '系统整体状态监控仪表板',
    layout: {
      columns: 12,
      rowHeight: 60,
      margin: [10, 10],
      containerPadding: [20, 20],
    },
    widgets: [
      {
        id: 'widget-001',
        type: 'metric',
        title: '插件总数',
        layout: { x: 0, y: 0, width: 3, height: 2 },
        dataBinding: {
          source: 'api',
          api_endpoint: '/api/plugin/status',
          refresh_interval: 5000,
        },
      },
      {
        id: 'widget-002',
        type: 'chart',
        title: '状态分布',
        layout: { x: 3, y: 0, width: 6, height: 4 },
        chartType: 'pie',
        chartOptions: {
          series: [{
            name: '插件状态',
            type: 'pie',
            data: [],
          }],
        },
        dataBinding: {
          source: 'api',
          api_endpoint: '/api/plugin/status',
          refresh_interval: 5000,
        },
      },
    ],
    theme: {
      primaryColor: '#1890ff',
      backgroundColor: '#f5f5f5',
      textColor: '#333333',
    },
    created_at: '2024-01-15T10:30:00Z',
    updated_at: '2024-01-20T14:45:00Z',
    created_by: 'admin',
  },
  {
    id: 'dashboard-002',
    name: '性能监控',
    description: '插件性能指标监控',
    layout: {
      columns: 12,
      rowHeight: 60,
      margin: [10, 10],
      containerPadding: [20, 20],
    },
    widgets: [
      {
        id: 'widget-003',
        type: 'chart',
        title: 'CPU 使用率',
        layout: { x: 0, y: 0, width: 6, height: 3 },
        chartType: 'line',
        chartOptions: {
          xAxis: { type: 'time' },
          yAxis: { type: 'value', max: 100 },
          series: [{
            name: 'CPU',
            type: 'line',
            data: [],
          }],
        },
        dataBinding: {
          source: 'api',
          api_endpoint: '/api/plugin/metrics',
          refresh_interval: 2000,
        },
      },
    ],
    created_at: '2024-01-16T09:15:00Z',
    updated_at: '2024-01-21T16:20:00Z',
    created_by: 'admin',
  },
];

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';

    let filteredDashboards = mockDashboards;

    // 搜索过滤
    if (search) {
      const searchLower = search.toLowerCase();
      filteredDashboards = filteredDashboards.filter(dashboard =>
        dashboard.name.toLowerCase().includes(searchLower) ||
        dashboard.description?.toLowerCase().includes(searchLower)
      );
    }

    // 分页
    const total = filteredDashboards.length;
    const pages = Math.ceil(total / limit);
    const offset = (page - 1) * limit;
    const paginatedData = filteredDashboards.slice(offset, offset + limit);

    const response: PaginatedResponse<DashboardConfig> = {
      success: true,
      data: paginatedData,
      pagination: {
        page,
        limit,
        total,
        pages,
      },
      timestamp: Date.now(),
    };

    return NextResponse.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : '获取仪表板配置失败',
      timestamp: Date.now(),
    };

    return NextResponse.json(response, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // 验证必需字段
    if (!body.name || !body.layout) {
      return NextResponse.json(
        {
          success: false,
          error: '缺少必需字段: name, layout',
          timestamp: Date.now(),
        },
        { status: 400 }
      );
    }

    // 创建新仪表板配置
    const newDashboard: DashboardConfig = {
      id: `dashboard-${Date.now()}`,
      name: body.name,
      description: body.description || '',
      layout: body.layout,
      widgets: body.widgets || [],
      theme: body.theme || {
        primaryColor: '#1890ff',
        backgroundColor: '#f5f5f5',
        textColor: '#333333',
      },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      created_by: 'admin', // 实际项目中应该从认证信息获取
    };

    // 在实际项目中，这里应该保存到数据库
    mockDashboards.push(newDashboard);

    const response: ApiResponse<DashboardConfig> = {
      success: true,
      data: newDashboard,
      message: '仪表板配置创建成功',
      timestamp: Date.now(),
    };

    return NextResponse.json(response, { status: 201 });
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : '创建仪表板配置失败',
      timestamp: Date.now(),
    };

    return NextResponse.json(response, { status: 500 });
  }
}
