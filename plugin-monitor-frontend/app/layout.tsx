import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import { AntdRegistry } from '@ant-design/nextjs-registry';
import { ThemeProvider, ThemeStyles } from '@/components/providers/theme-provider';
import { SWRProvider } from '@/components/providers/swr-provider';
import './globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Plugin Monitor Dashboard',
  description: 'Real-time plugin monitoring and HMI configuration interface',
  keywords: ['plugin', 'monitor', 'dashboard', 'HMI', 'real-time'],
  authors: [{ name: 'Beezer Team' }],
  viewport: 'width=device-width, initial-scale=1',
};



export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="zh-CN">
      <body className={inter.className}>
        <ThemeStyles />
        <AntdRegistry>
          <SWRProvider>
            <ThemeProvider>
              {children}
            </ThemeProvider>
          </SWRProvider>
        </AntdRegistry>
      </body>
    </html>
  );
}
