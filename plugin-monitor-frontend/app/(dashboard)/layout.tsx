'use client';

import React, { useState, useEffect } from 'react';
import { Layout, Menu, Button, Avatar, Dropdown, Badge } from 'antd';
import {
  DashboardOutlined,
  PluginOutlined,
  SettingOutlined,
  BellOutlined,
  UserOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  MonitorOutlined,
  AppstoreOutlined,
} from '@ant-design/icons';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { ThemeToggle } from '@/components/providers/theme-provider';
import { useConfigStore } from '@/lib/stores/config-store';
import { PerformanceMonitor } from '@/components/debug/performance-monitor';
import { DevToolsTrigger } from '@/components/debug/dev-tools';
import { PageErrorBoundary } from '@/components/debug/error-boundary-enhanced';

const { Header, Sider, Content } = Layout;

const menuItems = [
  {
    key: '/dashboard',
    icon: <DashboardOutlined />,
    label: <Link href="/dashboard">总览</Link>,
  },
  {
    key: '/dashboard/plugins',
    icon: <PluginOutlined />,
    label: <Link href="/dashboard/plugins">插件监控</Link>,
  },
  {
    key: '/dashboard/hmi',
    icon: <MonitorOutlined />,
    label: <Link href="/dashboard/hmi">HMI组态</Link>,
  },
  {
    key: '/dashboard/settings',
    icon: <SettingOutlined />,
    label: <Link href="/dashboard/settings">系统设置</Link>,
  },
];

const userMenuItems = [
  {
    key: 'profile',
    label: '个人资料',
    icon: <UserOutlined />,
  },
  {
    key: 'settings',
    label: '偏好设置',
    icon: <SettingOutlined />,
  },
  {
    type: 'divider' as const,
  },
  {
    key: 'logout',
    label: '退出登录',
    danger: true,
  },
];

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const {
    sidebarCollapsed,
    sidebarWidth,
    setSidebarCollapsed,
    loadUserConfig
  } = useConfigStore();

  // 初始化用户配置
  useEffect(() => {
    loadUserConfig();
  }, [loadUserConfig]);

  return (
    <PageErrorBoundary>
      <Layout className="min-h-screen">
        <Sider
          trigger={null}
          collapsible
          collapsed={sidebarCollapsed}
          className="shadow-lg"
          width={sidebarWidth}
        >
        <div className="flex items-center justify-center h-16 bg-white border-b">
          <div className="flex items-center space-x-2">
            <AppstoreOutlined className="text-2xl text-primary-500" />
            {!sidebarCollapsed && (
              <span className="text-lg font-semibold text-gray-800">
                Plugin Monitor
              </span>
            )}
          </div>
        </div>
        <Menu
          theme="light"
          mode="inline"
          selectedKeys={[pathname]}
          items={menuItems}
          className="border-r-0"
        />
      </Sider>
      
      <Layout>
        <Header className="bg-white shadow-sm px-4 flex items-center justify-between">
          <div className="flex items-center">
            <Button
              type="text"
              icon={sidebarCollapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
              className="text-lg"
            />
          </div>
          
          <div className="flex items-center space-x-4">
            <ThemeToggle />

            <Badge count={3} size="small">
              <Button
                type="text"
                icon={<BellOutlined />}
                className="text-lg"
              />
            </Badge>

            <Dropdown
              menu={{ items: userMenuItems }}
              placement="bottomRight"
              arrow
            >
              <div className="flex items-center space-x-2 cursor-pointer hover:bg-gray-50 px-2 py-1 rounded">
                <Avatar size="small" icon={<UserOutlined />} />
                <span className="text-sm text-gray-700">管理员</span>
              </div>
            </Dropdown>
          </div>
        </Header>
        
        <Content className="p-6 bg-gray-50">
          <div className="bg-white rounded-lg shadow-sm min-h-full">
            {children}
          </div>
        </Content>
      </Layout>

      {/* 调试工具 */}
      <PerformanceMonitor />
      <DevToolsTrigger />
    </Layout>
    </PageErrorBoundary>
  );
}
