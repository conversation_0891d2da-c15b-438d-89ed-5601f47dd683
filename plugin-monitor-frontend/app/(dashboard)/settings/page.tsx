'use client';

import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Tabs, 
  Form, 
  Input, 
  Select, 
  Switch, 
  Button, 
  Space, 
  message, 
  Upload,
  Modal,
  Divider,
  ColorPicker,
  Slider,
  InputNumber
} from 'antd';
import {
  SettingOutlined,
  UserOutlined,
  BgColorsOutlined,
  DownloadOutlined,
  UploadOutlined,
  ReloadOutlined,
  SaveOutlined,
} from '@ant-design/icons';
import { useConfigStore } from '@/lib/stores/config-store';

const { TabPane } = Tabs;
const { Option } = Select;

export default function SettingsPage() {
  const {
    userConfig,
    theme,
    customTheme,
    sidebarCollapsed,
    sidebarWidth,
    loading,
    error,
    updateUserConfig,
    saveUserConfig,
    resetUserConfig,
    setTheme,
    updateCustomTheme,
    setSidebarCollapsed,
    setSidebarWidth,
    exportConfig,
    importConfig,
    clearError,
  } = useConfigStore();

  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState('general');
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [importText, setImportText] = useState('');

  useEffect(() => {
    form.setFieldsValue(userConfig);
  }, [userConfig, form]);

  useEffect(() => {
    if (error) {
      message.error(error);
      clearError();
    }
  }, [error, clearError]);

  // 保存设置
  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      updateUserConfig(values);
      await saveUserConfig();
      message.success('设置已保存');
    } catch (error) {
      message.error('保存失败');
    }
  };

  // 重置设置
  const handleReset = () => {
    Modal.confirm({
      title: '确认重置',
      content: '确定要重置所有设置到默认值吗？此操作不可撤销。',
      onOk: () => {
        resetUserConfig();
        form.setFieldsValue(userConfig);
        message.success('设置已重置');
      },
    });
  };

  // 导出配置
  const handleExport = () => {
    const config = exportConfig();
    const blob = new Blob([config], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `plugin-monitor-config-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    message.success('配置已导出');
  };

  // 导入配置
  const handleImport = async () => {
    try {
      await importConfig(importText);
      setImportModalVisible(false);
      setImportText('');
      form.setFieldsValue(userConfig);
      message.success('配置已导入');
    } catch (error) {
      message.error('导入失败：配置文件格式错误');
    }
  };

  // 通用设置标签页
  const GeneralTab = () => (
    <Form form={form} layout="vertical" onFinish={handleSave}>
      <Card title="基本设置" size="small" className="mb-4">
        <Form.Item
          name="language"
          label="语言"
          tooltip="界面显示语言"
        >
          <Select>
            <Option value="zh-CN">简体中文</Option>
            <Option value="en-US">English</Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="timezone"
          label="时区"
          tooltip="数据显示时区"
        >
          <Select>
            <Option value="Asia/Shanghai">Asia/Shanghai (UTC+8)</Option>
            <Option value="UTC">UTC (UTC+0)</Option>
            <Option value="America/New_York">America/New_York (UTC-5)</Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="dateFormat"
          label="日期格式"
        >
          <Select>
            <Option value="YYYY-MM-DD HH:mm:ss">YYYY-MM-DD HH:mm:ss</Option>
            <Option value="MM/DD/YYYY HH:mm:ss">MM/DD/YYYY HH:mm:ss</Option>
            <Option value="DD/MM/YYYY HH:mm:ss">DD/MM/YYYY HH:mm:ss</Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="refreshInterval"
          label="刷新间隔 (毫秒)"
          tooltip="数据自动刷新间隔"
        >
          <InputNumber min={1000} max={60000} step={1000} />
        </Form.Item>
      </Card>

      <Card title="界面设置" size="small" className="mb-4">
        <Form.Item label="侧边栏">
          <Space>
            <Switch
              checked={!sidebarCollapsed}
              onChange={(checked) => setSidebarCollapsed(!checked)}
              checkedChildren="展开"
              unCheckedChildren="折叠"
            />
            <span>宽度:</span>
            <Slider
              value={sidebarWidth}
              onChange={setSidebarWidth}
              min={200}
              max={400}
              style={{ width: 100 }}
            />
            <span>{sidebarWidth}px</span>
          </Space>
        </Form.Item>
      </Card>

      <Card title="通知设置" size="small">
        <Form.Item
          name={['notifications', 'enabled']}
          label="启用通知"
          valuePropName="checked"
        >
          <Switch />
        </Form.Item>

        <Form.Item
          name={['notifications', 'sound']}
          label="声音提醒"
          valuePropName="checked"
        >
          <Switch />
        </Form.Item>

        <Form.Item
          name={['notifications', 'desktop']}
          label="桌面通知"
          valuePropName="checked"
        >
          <Switch />
        </Form.Item>

        <Form.Item
          name={['notifications', 'email']}
          label="邮件通知"
          valuePropName="checked"
        >
          <Switch />
        </Form.Item>
      </Card>
    </Form>
  );

  // 主题设置标签页
  const ThemeTab = () => (
    <div className="space-y-4">
      <Card title="主题选择" size="small">
        <div className="grid grid-cols-3 gap-4">
          {[
            { key: 'light', name: '亮色主题', preview: '#ffffff' },
            { key: 'dark', name: '暗色主题', preview: '#1f1f1f' },
            { key: 'auto', name: '自动切换', preview: 'linear-gradient(45deg, #ffffff 50%, #1f1f1f 50%)' },
          ].map((themeOption) => (
            <div
              key={themeOption.key}
              className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                theme === themeOption.key ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
              }`}
              onClick={() => setTheme(themeOption.key as any)}
            >
              <div
                className="w-full h-16 rounded mb-2"
                style={{ background: themeOption.preview }}
              />
              <div className="text-center text-sm font-medium">
                {themeOption.name}
              </div>
            </div>
          ))}
        </div>
      </Card>

      <Card title="自定义主题" size="small">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2">主色调</label>
            <ColorPicker
              value={customTheme.primaryColor || '#1890ff'}
              onChange={(color) => updateCustomTheme({
                ...customTheme,
                primaryColor: color.toHexString(),
              })}
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">成功色</label>
            <ColorPicker
              value={customTheme.successColor || '#52c41a'}
              onChange={(color) => updateCustomTheme({
                ...customTheme,
                successColor: color.toHexString(),
              })}
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">警告色</label>
            <ColorPicker
              value={customTheme.warningColor || '#faad14'}
              onChange={(color) => updateCustomTheme({
                ...customTheme,
                warningColor: color.toHexString(),
              })}
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">错误色</label>
            <ColorPicker
              value={customTheme.errorColor || '#ff4d4f'}
              onChange={(color) => updateCustomTheme({
                ...customTheme,
                errorColor: color.toHexString(),
              })}
            />
          </div>
        </div>
      </Card>
    </div>
  );

  // 数据管理标签页
  const DataTab = () => (
    <div className="space-y-4">
      <Card title="配置管理" size="small">
        <Space direction="vertical" className="w-full">
          <Button
            icon={<DownloadOutlined />}
            onClick={handleExport}
            block
          >
            导出配置
          </Button>
          
          <Button
            icon={<UploadOutlined />}
            onClick={() => setImportModalVisible(true)}
            block
          >
            导入配置
          </Button>
          
          <Divider />
          
          <Button
            icon={<ReloadOutlined />}
            onClick={handleReset}
            danger
            block
          >
            重置所有设置
          </Button>
        </Space>
      </Card>

      <Card title="缓存管理" size="small">
        <Space direction="vertical" className="w-full">
          <Button
            onClick={() => {
              localStorage.clear();
              message.success('本地缓存已清除');
            }}
            block
          >
            清除本地缓存
          </Button>
          
          <Button
            onClick={() => {
              sessionStorage.clear();
              message.success('会话缓存已清除');
            }}
            block
          >
            清除会话缓存
          </Button>
        </Space>
      </Card>
    </div>
  );

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800 mb-2">系统设置</h1>
        <p className="text-gray-600">个性化配置和系统管理</p>
      </div>

      <div className="bg-white rounded-lg shadow-sm">
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          tabBarExtraContent={
            <Space>
              <Button
                type="primary"
                icon={<SaveOutlined />}
                onClick={handleSave}
                loading={loading}
              >
                保存设置
              </Button>
            </Space>
          }
        >
          <TabPane
            tab={
              <span>
                <SettingOutlined />
                通用设置
              </span>
            }
            key="general"
          >
            <div className="p-6">
              <GeneralTab />
            </div>
          </TabPane>

          <TabPane
            tab={
              <span>
                <BgColorsOutlined />
                主题外观
              </span>
            }
            key="theme"
          >
            <div className="p-6">
              <ThemeTab />
            </div>
          </TabPane>

          <TabPane
            tab={
              <span>
                <UserOutlined />
                数据管理
              </span>
            }
            key="data"
          >
            <div className="p-6">
              <DataTab />
            </div>
          </TabPane>
        </Tabs>
      </div>

      {/* 导入配置模态框 */}
      <Modal
        title="导入配置"
        open={importModalVisible}
        onOk={handleImport}
        onCancel={() => {
          setImportModalVisible(false);
          setImportText('');
        }}
        width={600}
      >
        <Input.TextArea
          value={importText}
          onChange={(e) => setImportText(e.target.value)}
          placeholder="请粘贴配置文件内容..."
          rows={10}
        />
      </Modal>
    </div>
  );
}
