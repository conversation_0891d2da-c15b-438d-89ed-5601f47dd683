'use client';

import React, { useState } from 'react';
import { Layout, Card, Button, Space, message, Drawer, Tabs } from 'antd';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import {
  AppstoreAddOutlined,
  SaveOutlined,
  EyeOutlined,
  SettingOutlined,
  DragOutlined,
} from '@ant-design/icons';
import { Canvas, CanvasWidget } from '@/components/hmi/canvas';
import { WidgetLibrary, defaultWidgetLibrary } from '@/components/hmi/widget-library';

const { Sider, Content } = Layout;
const { TabPane } = Tabs;

export default function HMIPage() {
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [widgets, setWidgets] = useState<CanvasWidget[]>([]);
  const [selectedWidgetId, setSelectedWidgetId] = useState<string>();
  const [propertyDrawerOpen, setPropertyDrawerOpen] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [zoom, setZoom] = useState(1);
  const [showGrid, setShowGrid] = useState(true);

  // 添加组件
  const handleWidgetAdd = (widget: Omit<CanvasWidget, 'id'>) => {
    const newWidget: CanvasWidget = {
      ...widget,
      id: `widget-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    };
    setWidgets([...widgets, newWidget]);
    setSelectedWidgetId(newWidget.id);
    message.success('组件已添加');
  };

  // 更新组件
  const handleWidgetUpdate = (id: string, updates: Partial<CanvasWidget>) => {
    setWidgets(widgets.map(w => w.id === id ? { ...w, ...updates } : w));
  };

  // 删除组件
  const handleWidgetDelete = (id: string) => {
    setWidgets(widgets.filter(w => w.id !== id));
    if (selectedWidgetId === id) {
      setSelectedWidgetId(undefined);
    }
  };

  // 复制组件
  const handleWidgetCopy = (id: string) => {
    const widget = widgets.find(w => w.id === id);
    if (widget) {
      const newWidget: CanvasWidget = {
        ...widget,
        id: `widget-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        position: {
          ...widget.position,
          x: widget.position.x + 20,
          y: widget.position.y + 20,
        },
      };
      setWidgets([...widgets, newWidget]);
      setSelectedWidgetId(newWidget.id);
    }
  };

  // 选择组件
  const handleWidgetSelect = (id: string | undefined) => {
    setSelectedWidgetId(id);
    if (id) {
      setPropertyDrawerOpen(true);
    }
  };

  // 保存配置
  const handleSave = () => {
    // TODO: 保存配置到后端
    const config = {
      widgets,
      settings: {
        zoom,
        showGrid,
      },
    };
    console.log('保存配置:', config);
    message.success('配置已保存');
  };

  // 切换预览模式
  const togglePreview = () => {
    setIsPreviewMode(!isPreviewMode);
    setSelectedWidgetId(undefined);
    message.info(isPreviewMode ? '已切换到编辑模式' : '已切换到预览模式');
  };

  // 渲染组件
  const renderWidget = (widget: CanvasWidget) => {
    // 根据组件类型渲染不同的组件
    switch (widget.type) {
      case 'text-display':
        return (
          <div className="w-full h-full bg-white border rounded shadow-sm flex items-center justify-center">
            <span style={{
              fontSize: widget.config.fontSize || 14,
              color: widget.config.color || '#333333',
            }}>
              {widget.config.text || '文本内容'}
            </span>
          </div>
        );
      case 'number-display':
        return (
          <div className="w-full h-full bg-white border rounded shadow-sm flex items-center justify-center">
            <div className="text-center">
              <div style={{
                fontSize: widget.config.fontSize || 24,
                color: widget.config.color || '#1890ff',
                fontWeight: 'bold',
              }}>
                {widget.config.value || 0}{widget.config.unit || ''}
              </div>
            </div>
          </div>
        );
      default:
        return (
          <div className="w-full h-full bg-white border rounded shadow-sm flex items-center justify-center">
            <div className="text-center">
              <div className="text-2xl mb-2">📊</div>
              <div className="text-sm font-medium">{widget.type}</div>
            </div>
          </div>
        );
    }
  };

  const selectedWidget = selectedWidgetId ? widgets.find(w => w.id === selectedWidgetId) : null;

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="h-full">
        <Layout className="h-full">
          {/* 左侧组件库 */}
          {!isPreviewMode && (
            <Sider width={280} className="bg-white border-r">
              <WidgetLibrary
                items={defaultWidgetLibrary}
                searchText={searchText}
                onSearchChange={setSearchText}
                selectedCategory={selectedCategory}
                onCategoryChange={setSelectedCategory}
              />
            </Sider>
          )}

          {/* 主内容区 */}
          <Layout>
            {/* 工具栏 */}
            <div className="bg-white border-b px-4 py-2">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-lg font-semibold">HMI 组态设计器</h2>
                  <div className="text-sm text-gray-500">
                    组件数量: {widgets.length} | 缩放: {Math.round(zoom * 100)}%
                  </div>
                </div>
                <Space>
                  <Button
                    icon={<EyeOutlined />}
                    onClick={togglePreview}
                    type={isPreviewMode ? 'primary' : 'default'}
                  >
                    {isPreviewMode ? '编辑模式' : '预览模式'}
                  </Button>
                  <Button icon={<SaveOutlined />} onClick={handleSave}>
                    保存
                  </Button>
                  <Button icon={<SettingOutlined />}>
                    设置
                  </Button>
                </Space>
              </div>
            </div>

            {/* 画布区域 */}
            <Content className="bg-gray-100">
              <Canvas
                widgets={widgets}
                selectedWidgetId={selectedWidgetId}
                isPreviewMode={isPreviewMode}
                showGrid={showGrid}
                zoom={zoom}
                onWidgetAdd={handleWidgetAdd}
                onWidgetUpdate={handleWidgetUpdate}
                onWidgetDelete={handleWidgetDelete}
                onWidgetSelect={handleWidgetSelect}
                onWidgetCopy={handleWidgetCopy}
                onZoomChange={setZoom}
                renderWidget={renderWidget}
              />
            </Content>
          </Layout>
      </Layout>

        {/* 属性配置抽屉 */}
        <Drawer
          title="组件属性"
          placement="right"
          width={320}
          open={propertyDrawerOpen}
          onClose={() => setPropertyDrawerOpen(false)}
        >
          {selectedWidget && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">组件类型</label>
                <div className="text-sm text-gray-600">{selectedWidget.type}</div>
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">位置</label>
                <div className="text-sm text-gray-600">
                  X: {selectedWidget.position.x}px, Y: {selectedWidget.position.y}px
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">尺寸</label>
                <div className="text-sm text-gray-600">
                  宽: {selectedWidget.position.width}px, 高: {selectedWidget.position.height}px
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">配置</label>
                <pre className="text-xs bg-gray-50 p-2 rounded overflow-auto">
                  {JSON.stringify(selectedWidget.config, null, 2)}
                </pre>
              </div>
              {/* TODO: 添加更多属性配置 */}
            </div>
          )}
        </Drawer>
      </div>
    </DndProvider>
  );
}
