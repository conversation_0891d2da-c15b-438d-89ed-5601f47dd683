# Plugin Monitor Frontend

基于 Next.js 14 的插件监控前端界面，提供实时插件状态监控、HMI组态设计和数据可视化功能。

## 功能特性

- 🔍 **实时插件监控** - 监控所有插件的运行状态和性能指标
- 🎨 **HMI 组态设计** - 可视化拖拽设计监控界面
- 📊 **数据可视化** - 丰富的图表和仪表盘组件
- 🔧 **配置管理** - 插件配置和用户偏好设置
- 📱 **响应式设计** - 适配桌面和移动设备
- ⚡ **实时通信** - WebSocket 实时数据更新

## 技术栈

- **框架**: Next.js 14 (App Router)
- **UI 库**: Ant Design 5.x
- **样式**: Tailwind CSS
- **状态管理**: Zustand
- **图表**: ECharts
- **拖拽**: React DnD
- **语言**: TypeScript

## 快速开始

### 安装依赖

```bash
npm install
# 或
yarn install
# 或
pnpm install
```

### 开发环境

```bash
npm run dev
# 或
yarn dev
# 或
pnpm dev
```

打开 [http://localhost:3000](http://localhost:3000) 查看应用。

### 构建生产版本

```bash
npm run build
npm run start
```

## 项目结构

```
plugin-monitor-frontend/
├── app/                    # Next.js App Router
│   ├── (dashboard)/       # 仪表板路由组
│   │   ├── layout.tsx     # 仪表板布局
│   │   ├── page.tsx       # 首页
│   │   ├── plugins/       # 插件监控页面
│   │   ├── hmi/          # HMI 组态页面
│   │   └── settings/     # 设置页面
│   ├── api/              # API Routes
│   ├── globals.css       # 全局样式
│   └── layout.tsx        # 根布局
├── components/           # React 组件
│   ├── ui/              # 基础 UI 组件
│   ├── plugin/          # 插件相关组件
│   └── charts/          # 图表组件
├── lib/                 # 工具库
│   ├── types/           # TypeScript 类型定义
│   ├── stores/          # Zustand 状态管理
│   ├── services/        # API 服务
│   └── utils/           # 工具函数
└── public/              # 静态资源
```

## 环境变量

复制 `.env.local` 文件并根据需要修改配置：

```bash
cp .env.local .env.local
```

主要配置项：

- `NEXT_PUBLIC_API_URL`: 前端 API 基础 URL
- `BEEZER_API_URL`: Beezer 后端服务 URL
- `WEBSOCKET_URL`: WebSocket 服务 URL

## 开发指南

### 添加新页面

在 `app/(dashboard)/` 目录下创建新的页面文件：

```typescript
// app/(dashboard)/new-page/page.tsx
export default function NewPage() {
  return <div>新页面</div>;
}
```

### 添加新组件

在 `components/` 目录下创建组件：

```typescript
// components/ui/new-component.tsx
interface NewComponentProps {
  // 组件属性
}

export const NewComponent: React.FC<NewComponentProps> = (props) => {
  return <div>新组件</div>;
};
```

### 状态管理

使用 Zustand 进行状态管理：

```typescript
// lib/stores/new-store.ts
import { create } from 'zustand';

interface NewStore {
  data: any[];
  loading: boolean;
  fetchData: () => Promise<void>;
}

export const useNewStore = create<NewStore>((set) => ({
  data: [],
  loading: false,
  fetchData: async () => {
    set({ loading: true });
    // 获取数据逻辑
    set({ loading: false });
  },
}));
```

### API 服务

在 `lib/services/` 目录下添加 API 服务：

```typescript
// lib/services/new-api.ts
export const newApi = {
  async getData() {
    const response = await fetch('/api/data');
    return response.json();
  },
};
```

## 部署

### Vercel 部署

1. 将代码推送到 GitHub
2. 在 Vercel 中导入项目
3. 配置环境变量
4. 部署

### Docker 部署

```bash
# 构建镜像
docker build -t plugin-monitor-frontend .

# 运行容器
docker run -p 3000:3000 plugin-monitor-frontend
```

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。
