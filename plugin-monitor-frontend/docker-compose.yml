version: '3.8'

services:
  # Plugin Monitor Frontend
  frontend:
    build:
      context: .
      dockerfile: Dockerfile
      target: runner
    container_name: plugin-monitor-frontend
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - BEEZER_API_URL=http://backend:9999
      - WEBSOCKET_URL=ws://backend:9999
    depends_on:
      - backend
    networks:
      - plugin-monitor-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "/app/healthcheck.sh"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    volumes:
      - frontend-logs:/app/logs
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.frontend.rule=Host(`localhost`)"
      - "traefik.http.services.frontend.loadbalancer.server.port=3000"

  # Plugin Monitor Backend (模拟)
  backend:
    image: nginx:alpine
    container_name: plugin-monitor-backend
    ports:
      - "9999:80"
    volumes:
      - ./mock-api:/usr/share/nginx/html
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    networks:
      - plugin-monitor-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis (用于缓存和会话存储)
  redis:
    image: redis:7-alpine
    container_name: plugin-monitor-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis123}
    volumes:
      - redis-data:/data
    networks:
      - plugin-monitor-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: plugin-monitor-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx-logs:/var/log/nginx
    depends_on:
      - frontend
      - backend
    networks:
      - plugin-monitor-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus 监控
  prometheus:
    image: prom/prometheus:latest
    container_name: plugin-monitor-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - plugin-monitor-network
    restart: unless-stopped

  # Grafana 可视化
  grafana:
    image: grafana/grafana:latest
    container_name: plugin-monitor-grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    networks:
      - plugin-monitor-network
    restart: unless-stopped

networks:
  plugin-monitor-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  frontend-logs:
    driver: local
  redis-data:
    driver: local
  nginx-logs:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local

# 开发环境覆盖
---
version: '3.8'

# docker-compose.override.yml (开发环境)
services:
  frontend:
    build:
      target: deps
    command: npm run dev
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next
    environment:
      - NODE_ENV=development
      - CHOKIDAR_USEPOLLING=true
    ports:
      - "3000:3000"
      - "3001:3001"  # 用于热重载

  backend:
    volumes:
      - ./mock-api:/usr/share/nginx/html
      - ./dev-nginx.conf:/etc/nginx/nginx.conf:ro

# 生产环境配置
---
version: '3.8'

# docker-compose.prod.yml (生产环境)
services:
  frontend:
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '1'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  nginx:
    deploy:
      replicas: 1
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
